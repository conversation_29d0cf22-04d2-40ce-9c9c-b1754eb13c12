-- CarShopClient.lua - Client-side car shop GUI and functionality
-- Place this script in StarterPlayer > StarterPlayerScripts

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

-- Player references
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Remote events
local remotes = ReplicatedStorage:WaitForChild("RemoteEvents")
local purchaseRequest = remotes:WaitForChild("PurchaseRequest")
local spawnCarRequest = remotes:WaitFor<PERSON>hild("SpawnCarRequest")

-- Car shop GUI
local carShopGui = nil
local isShopOpen = false

-- Player data
local playerData = {
    Cash = 500,
    Level = 1,
    OwnedCars = {"BasicCar"}
}

-- Car configurations (client-side copy for display)
local carConfigs = {
    BasicCar = {
        Name = "Basic Car",
        Price = 0,
        Speed = 50,
        Handling = 60,
        Category = "Civilian",
        RequiredLevel = 1,
        Description = "A simple starter car for getting around the city.",
        Image = "rbxasset://textures/face.png" -- Replace with actual car images
    },
    
    FamilyCar = {
        Name = "Family Car",
        Price = 1500,
        Speed = 55,
        Handling = 65,
        Category = "Civilian",
        RequiredLevel = 3,
        Description = "Reliable family vehicle with good comfort.",
        Image = "rbxasset://textures/face.png"
    },
    
    SportsCar = {
        Name = "Sports Car",
        Price = 5000,
        Speed = 80,
        Handling = 85,
        Category = "Civilian",
        RequiredLevel = 10,
        Description = "Fast and stylish sports car for racing enthusiasts.",
        Image = "rbxasset://textures/face.png"
    },
    
    PoliceCruiser = {
        Name = "Police Cruiser",
        Price = 0,
        Speed = 70,
        Handling = 75,
        Category = "Police",
        RequiredLevel = 1,
        Description = "Standard police vehicle for patrol duties.",
        Image = "rbxasset://textures/face.png",
        RoleRequired = "Police"
    },
    
    GetawayCar = {
        Name = "Getaway Car",
        Price = 3000,
        Speed = 85,
        Handling = 80,
        Category = "Criminal",
        RequiredLevel = 5,
        Description = "Fast car perfect for quick escapes.",
        Image = "rbxasset://textures/face.png"
    },
    
    Motorcycle = {
        Name = "Motorcycle",
        Price = 2000,
        Speed = 90,
        Handling = 95,
        Category = "Criminal",
        RequiredLevel = 7,
        Description = "Agile bike for weaving through traffic.",
        Image = "rbxasset://textures/face.png"
    },
    
    Supercar = {
        Name = "Supercar",
        Price = 0,
        Speed = 100,
        Handling = 90,
        Category = "Special",
        RequiredLevel = 1,
        Description = "Ultimate speed machine for VIP players.",
        Image = "rbxasset://textures/face.png",
        GamepassRequired = "SupercarPack"
    }
}

-- Create car shop GUI
local function createCarShopGui()
    carShopGui = Instance.new("ScreenGui")
    carShopGui.Name = "CarShopGui"
    carShopGui.ResetOnSpawn = false
    carShopGui.Enabled = false
    carShopGui.Parent = playerGui
    
    -- Main frame
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 800, 0, 600)
    mainFrame.Position = UDim2.new(0.5, -400, 0.5, -300)
    mainFrame.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    mainFrame.BorderSizePixel = 0
    mainFrame.Parent = carShopGui
    
    local mainCorner = Instance.new("UICorner")
    mainCorner.CornerRadius = UDim.new(0, 12)
    mainCorner.Parent = mainFrame
    
    -- Title bar
    local titleBar = Instance.new("Frame")
    titleBar.Name = "TitleBar"
    titleBar.Size = UDim2.new(1, 0, 0, 50)
    titleBar.Position = UDim2.new(0, 0, 0, 0)
    titleBar.BackgroundColor3 = Color3.fromRGB(0, 120, 255)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainFrame
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 12)
    titleCorner.Parent = titleBar
    
    -- Fix corner for title bar
    local titleFix = Instance.new("Frame")
    titleFix.Size = UDim2.new(1, 0, 0, 12)
    titleFix.Position = UDim2.new(0, 0, 1, -12)
    titleFix.BackgroundColor3 = Color3.fromRGB(0, 120, 255)
    titleFix.BorderSizePixel = 0
    titleFix.Parent = titleBar
    
    -- Title text
    local titleText = Instance.new("TextLabel")
    titleText.Name = "TitleText"
    titleText.Size = UDim2.new(1, -100, 1, 0)
    titleText.Position = UDim2.new(0, 20, 0, 0)
    titleText.BackgroundTransparency = 1
    titleText.Text = "🚗 CAR SHOP"
    titleText.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleText.TextScaled = true
    titleText.Font = Enum.Font.GothamBold
    titleText.TextXAlignment = Enum.TextXAlignment.Left
    titleText.Parent = titleBar
    
    -- Close button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -45, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(255, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.GothamBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    -- Player info frame
    local infoFrame = Instance.new("Frame")
    infoFrame.Name = "InfoFrame"
    infoFrame.Size = UDim2.new(1, -20, 0, 60)
    infoFrame.Position = UDim2.new(0, 10, 0, 60)
    infoFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    infoFrame.BorderSizePixel = 0
    infoFrame.Parent = mainFrame
    
    local infoCorner = Instance.new("UICorner")
    infoCorner.CornerRadius = UDim.new(0, 8)
    infoCorner.Parent = infoFrame
    
    -- Cash display
    local cashText = Instance.new("TextLabel")
    cashText.Name = "CashText"
    cashText.Size = UDim2.new(0.5, -10, 1, -20)
    cashText.Position = UDim2.new(0, 10, 0, 10)
    cashText.BackgroundTransparency = 1
    cashText.Text = "💰 Cash: $500"
    cashText.TextColor3 = Color3.fromRGB(0, 255, 100)
    cashText.TextScaled = true
    cashText.Font = Enum.Font.GothamBold
    cashText.TextXAlignment = Enum.TextXAlignment.Left
    cashText.Parent = infoFrame
    
    -- Level display
    local levelText = Instance.new("TextLabel")
    levelText.Name = "LevelText"
    levelText.Size = UDim2.new(0.5, -10, 1, -20)
    levelText.Position = UDim2.new(0.5, 0, 0, 10)
    levelText.BackgroundTransparency = 1
    levelText.Text = "⭐ Level: 1"
    levelText.TextColor3 = Color3.fromRGB(255, 255, 100)
    levelText.TextScaled = true
    levelText.Font = Enum.Font.GothamBold
    levelText.TextXAlignment = Enum.TextXAlignment.Left
    levelText.Parent = infoFrame
    
    -- Category tabs
    local tabFrame = Instance.new("Frame")
    tabFrame.Name = "TabFrame"
    tabFrame.Size = UDim2.new(1, -20, 0, 40)
    tabFrame.Position = UDim2.new(0, 10, 0, 130)
    tabFrame.BackgroundTransparency = 1
    tabFrame.Parent = mainFrame
    
    local categories = {"All", "Civilian", "Police", "Criminal", "Special"}
    local tabButtons = {}
    
    for i, category in ipairs(categories) do
        local tabButton = Instance.new("TextButton")
        tabButton.Name = category .. "Tab"
        tabButton.Size = UDim2.new(1/#categories, -5, 1, 0)
        tabButton.Position = UDim2.new((i-1)/#categories, (i-1)*5, 0, 0)
        tabButton.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
        tabButton.Text = category
        tabButton.TextColor3 = Color3.fromRGB(200, 200, 200)
        tabButton.TextScaled = true
        tabButton.Font = Enum.Font.Gotham
        tabButton.BorderSizePixel = 0
        tabButton.Parent = tabFrame
        
        local tabCorner = Instance.new("UICorner")
        tabCorner.CornerRadius = UDim.new(0, 6)
        tabCorner.Parent = tabButton
        
        tabButtons[category] = tabButton
        
        tabButton.MouseButton1Click:Connect(function()
            selectCategory(category)
        end)
    end
    
    -- Car list scroll frame
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Name = "CarList"
    scrollFrame.Size = UDim2.new(1, -20, 1, -220)
    scrollFrame.Position = UDim2.new(0, 10, 0, 180)
    scrollFrame.BackgroundColor3 = Color3.fromRGB(25, 25, 25)
    scrollFrame.BorderSizePixel = 0
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.ScrollBarImageColor3 = Color3.fromRGB(100, 100, 100)
    scrollFrame.Parent = mainFrame
    
    local scrollCorner = Instance.new("UICorner")
    scrollCorner.CornerRadius = UDim.new(0, 8)
    scrollCorner.Parent = scrollFrame
    
    -- Grid layout for cars
    local gridLayout = Instance.new("UIGridLayout")
    gridLayout.CellSize = UDim2.new(0, 180, 0, 220)
    gridLayout.CellPadding = UDim2.new(0, 10, 0, 10)
    gridLayout.SortOrder = Enum.SortOrder.Name
    gridLayout.Parent = scrollFrame
    
    local gridPadding = Instance.new("UIPadding")
    gridPadding.PaddingTop = UDim.new(0, 10)
    gridPadding.PaddingLeft = UDim.new(0, 10)
    gridPadding.PaddingRight = UDim.new(0, 10)
    gridPadding.PaddingBottom = UDim.new(0, 10)
    gridPadding.Parent = scrollFrame
    
    -- Connect close button
    closeButton.MouseButton1Click:Connect(function()
        closeCarShop()
    end)
    
    -- Select first category
    selectCategory("All")
    
    print("CarShopClient: Created car shop GUI")
end

-- Create car item in the shop
local function createCarItem(carType, config, parent)
    local carFrame = Instance.new("Frame")
    carFrame.Name = carType
    carFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    carFrame.BorderSizePixel = 0
    carFrame.Parent = parent
    
    local carCorner = Instance.new("UICorner")
    carCorner.CornerRadius = UDim.new(0, 8)
    carCorner.Parent = carFrame
    
    -- Car image
    local carImage = Instance.new("ImageLabel")
    carImage.Name = "CarImage"
    carImage.Size = UDim2.new(1, -20, 0, 80)
    carImage.Position = UDim2.new(0, 10, 0, 10)
    carImage.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    carImage.Image = config.Image
    carImage.ScaleType = Enum.ScaleType.Fit
    carImage.BorderSizePixel = 0
    carImage.Parent = carFrame
    
    local imageCorner = Instance.new("UICorner")
    imageCorner.CornerRadius = UDim.new(0, 6)
    imageCorner.Parent = carImage
    
    -- Car name
    local carName = Instance.new("TextLabel")
    carName.Name = "CarName"
    carName.Size = UDim2.new(1, -20, 0, 25)
    carName.Position = UDim2.new(0, 10, 0, 95)
    carName.BackgroundTransparency = 1
    carName.Text = config.Name
    carName.TextColor3 = Color3.fromRGB(255, 255, 255)
    carName.TextScaled = true
    carName.Font = Enum.Font.GothamBold
    carName.Parent = carFrame
    
    -- Car stats
    local statsText = Instance.new("TextLabel")
    statsText.Name = "StatsText"
    statsText.Size = UDim2.new(1, -20, 0, 30)
    statsText.Position = UDim2.new(0, 10, 0, 120)
    statsText.BackgroundTransparency = 1
    statsText.Text = string.format("Speed: %d | Handling: %d", config.Speed, config.Handling)
    statsText.TextColor3 = Color3.fromRGB(200, 200, 200)
    statsText.TextScaled = true
    statsText.Font = Enum.Font.Gotham
    statsText.Parent = carFrame
    
    -- Price/Status
    local priceText = Instance.new("TextLabel")
    priceText.Name = "PriceText"
    priceText.Size = UDim2.new(1, -20, 0, 20)
    priceText.Position = UDim2.new(0, 10, 0, 150)
    priceText.BackgroundTransparency = 1
    priceText.TextColor3 = Color3.fromRGB(0, 255, 100)
    priceText.TextScaled = true
    priceText.Font = Enum.Font.GothamBold
    priceText.Parent = carFrame
    
    -- Action button
    local actionButton = Instance.new("TextButton")
    actionButton.Name = "ActionButton"
    actionButton.Size = UDim2.new(1, -20, 0, 30)
    actionButton.Position = UDim2.new(0, 10, 0, 175)
    actionButton.BorderSizePixel = 0
    actionButton.TextScaled = true
    actionButton.Font = Enum.Font.GothamBold
    actionButton.Parent = carFrame
    
    local buttonCorner = Instance.new("UICorner")
    buttonCorner.CornerRadius = UDim.new(0, 6)
    buttonCorner.Parent = actionButton
    
    -- Check ownership and requirements
    local owned = table.find(playerData.OwnedCars, carType) ~= nil
    local canAfford = playerData.Cash >= config.Price
    local levelMet = playerData.Level >= config.RequiredLevel
    local hasGamepass = not config.GamepassRequired -- Assume true for now
    
    if owned then
        priceText.Text = "OWNED"
        priceText.TextColor3 = Color3.fromRGB(0, 255, 0)
        actionButton.Text = "SPAWN"
        actionButton.BackgroundColor3 = Color3.fromRGB(0, 150, 255)
        actionButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        
        actionButton.MouseButton1Click:Connect(function()
            spawnCar(carType)
        end)
        
    elseif config.Price == 0 and config.GamepassRequired then
        priceText.Text = "GAMEPASS REQUIRED"
        priceText.TextColor3 = Color3.fromRGB(255, 200, 0)
        actionButton.Text = "GET GAMEPASS"
        actionButton.BackgroundColor3 = Color3.fromRGB(255, 150, 0)
        actionButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        
    elseif not levelMet then
        priceText.Text = string.format("LEVEL %d REQUIRED", config.RequiredLevel)
        priceText.TextColor3 = Color3.fromRGB(255, 100, 100)
        actionButton.Text = "LOCKED"
        actionButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
        actionButton.TextColor3 = Color3.fromRGB(150, 150, 150)
        
    elseif not canAfford then
        priceText.Text = string.format("$%d", config.Price)
        priceText.TextColor3 = Color3.fromRGB(255, 100, 100)
        actionButton.Text = "CAN'T AFFORD"
        actionButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
        actionButton.TextColor3 = Color3.fromRGB(150, 150, 150)
        
    else
        priceText.Text = string.format("$%d", config.Price)
        priceText.TextColor3 = Color3.fromRGB(0, 255, 100)
        actionButton.Text = "BUY"
        actionButton.BackgroundColor3 = Color3.fromRGB(0, 200, 0)
        actionButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        
        actionButton.MouseButton1Click:Connect(function()
            buyCar(carType, config)
        end)
    end
end

-- Select category and update display
function selectCategory(category)
    if not carShopGui then return end
    
    local tabFrame = carShopGui.MainFrame.TabFrame
    local scrollFrame = carShopGui.MainFrame.CarList
    
    -- Update tab appearance
    for _, tab in pairs(tabFrame:GetChildren()) do
        if tab:IsA("TextButton") then
            if tab.Name == category .. "Tab" then
                tab.BackgroundColor3 = Color3.fromRGB(0, 120, 255)
                tab.TextColor3 = Color3.fromRGB(255, 255, 255)
            else
                tab.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
                tab.TextColor3 = Color3.fromRGB(200, 200, 200)
            end
        end
    end
    
    -- Clear existing cars
    for _, child in pairs(scrollFrame:GetChildren()) do
        if child:IsA("Frame") then
            child:Destroy()
        end
    end
    
    -- Add cars for selected category
    for carType, config in pairs(carConfigs) do
        if category == "All" or config.Category == category then
            createCarItem(carType, config, scrollFrame)
        end
    end
    
    -- Update scroll canvas size
    local gridLayout = scrollFrame:FindFirstChildOfClass("UIGridLayout")
    if gridLayout then
        scrollFrame.CanvasSize = UDim2.new(0, 0, 0, gridLayout.AbsoluteContentSize.Y + 20)
    end
end

-- Buy a car
function buyCar(carType, config)
    local success, message = purchaseRequest:InvokeServer("Car", carType)
    
    if success then
        -- Update player data
        playerData.Cash = playerData.Cash - config.Price
        table.insert(playerData.OwnedCars, carType)
        
        -- Update display
        updatePlayerInfo()
        selectCategory("All") -- Refresh the display
        
        print("CarShopClient: Successfully bought", config.Name)
    else
        print("CarShopClient: Failed to buy car -", message)
    end
end

-- Spawn a car
function spawnCar(carType)
    local success, message = spawnCarRequest:InvokeServer(carType)
    
    if success then
        print("CarShopClient: Successfully spawned", carType)
        closeCarShop()
    else
        print("CarShopClient: Failed to spawn car -", message)
    end
end

-- Update player info display
function updatePlayerInfo()
    if not carShopGui then return end
    
    local infoFrame = carShopGui.MainFrame.InfoFrame
    local cashText = infoFrame:FindFirstChild("CashText")
    local levelText = infoFrame:FindFirstChild("LevelText")
    
    if cashText then
        cashText.Text = string.format("💰 Cash: $%d", playerData.Cash)
    end
    
    if levelText then
        levelText.Text = string.format("⭐ Level: %d", playerData.Level)
    end
end

-- Open car shop
function openCarShop()
    if not carShopGui then
        createCarShopGui()
    end
    
    carShopGui.Enabled = true
    isShopOpen = true
    
    -- Update player info
    updatePlayerInfo()
    
    -- Animate opening
    local mainFrame = carShopGui.MainFrame
    mainFrame.Size = UDim2.new(0, 0, 0, 0)
    mainFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
    
    local openTween = TweenService:Create(mainFrame, 
        TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
        {
            Size = UDim2.new(0, 800, 0, 600),
            Position = UDim2.new(0.5, -400, 0.5, -300)
        }
    )
    openTween:Play()
    
    print("CarShopClient: Opened car shop")
end

-- Close car shop
function closeCarShop()
    if not carShopGui then return end
    
    -- Animate closing
    local mainFrame = carShopGui.MainFrame
    local closeTween = TweenService:Create(mainFrame,
        TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In),
        {
            Size = UDim2.new(0, 0, 0, 0),
            Position = UDim2.new(0.5, 0, 0.5, 0)
        }
    )
    
    closeTween:Play()
    closeTween.Completed:Connect(function()
        carShopGui.Enabled = false
        isShopOpen = false
    end)
    
    print("CarShopClient: Closed car shop")
end

-- Handle player data updates
local function onPlayerDataUpdated(key, value)
    playerData[key] = value
    
    if isShopOpen then
        updatePlayerInfo()
        
        -- Refresh display if cash or level changed
        if key == "Cash" or key == "Level" then
            selectCategory("All")
        end
    end
end

-- Initialize
local function initialize()
    print("CarShopClient: Initializing...")
    
    -- Connect to player data updates
    local playerDataUpdated = remotes:WaitForChild("PlayerDataUpdated")
    playerDataUpdated.OnClientEvent:Connect(onPlayerDataUpdated)
    
    -- Connect input for opening shop
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.C then
            if isShopOpen then
                closeCarShop()
            else
                openCarShop()
            end
        end
    end)
    
    print("CarShopClient: Initialized successfully! Press C to open car shop")
end

-- Start the client
initialize()

-- Export functions for other scripts
_G.CarShopClient = {
    OpenShop = openCarShop,
    CloseShop = closeCarShop
}
