-- PlayerData.lua - Player data management module
local PlayerData = {}

-- Services
local Players = game:GetService("Players")
local DataStoreService = game:GetService("DataStoreService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- DataStore
local PlayerDataStore = DataStoreService:GetDataStore("PlayerData_v1")

-- Default player data structure
PlayerData.DefaultData = {
    Cash = 500,
    Level = 1,
    XP = 0,
    XPToNextLevel = 100,
    
    -- Statistics
    Stats = {
        GamesPlayed = 0,
        CrimesCommitted = 0,
        CriminalsArrested = 0,
        EscapesSuccessful = 0,
        TimesArrested = 0
    },
    
    -- Owned items
    OwnedCars = {"BasicCar"},
    OwnedSkins = {"Default"},
    OwnedAccessories = {},
    
    -- Settings
    Settings = {
        SoundEnabled = true,
        MusicVolume = 0.5,
        SFXVolume = 0.7,
        PreferredRole = "Any" -- "Criminal", "Police", "Citizen", "Any"
    },
    
    -- VIP/Gamepass data
    Gamepasses = {
        VIPPass = false,
        SupercarPack = false,
        SWATPack = false,
        PrisonEscapePass = false,
        GarageExpansion = false
    }
}

-- Player data cache
PlayerData.Cache = {}

-- Events
PlayerData.Events = {
    DataLoaded = Instance.new("BindableEvent"),
    DataUpdated = Instance.new("BindableEvent"),
    CashChanged = Instance.new("BindableEvent"),
    LevelUp = Instance.new("BindableEvent")
}

-- Initialize PlayerData
function PlayerData:Initialize()
    print("PlayerData: Initializing...")
    
    -- Connect player events
    Players.PlayerAdded:Connect(function(player)
        self:LoadPlayerData(player)
    end)
    
    Players.PlayerRemoving:Connect(function(player)
        self:SavePlayerData(player)
    end)
    
    -- Auto-save every 5 minutes
    spawn(function()
        while true do
            wait(300) -- 5 minutes
            self:SaveAllPlayerData()
        end
    end)
    
    print("PlayerData: Initialized successfully!")
end

-- Data Loading
function PlayerData:LoadPlayerData(player)
    print("PlayerData: Loading data for", player.Name)
    
    local success, data = pcall(function()
        return PlayerDataStore:GetAsync(player.UserId)
    end)
    
    if success and data then
        -- Merge with default data to ensure all fields exist
        self.Cache[player] = self:MergeData(self.DefaultData, data)
        print("PlayerData: Loaded existing data for", player.Name)
    else
        -- Create new player data
        self.Cache[player] = self:DeepCopy(self.DefaultData)
        print("PlayerData: Created new data for", player.Name)
    end
    
    -- Create leaderstats
    self:CreateLeaderstats(player)
    
    -- Fire data loaded event
    self.Events.DataLoaded:Fire(player)
end

-- Data Saving
function PlayerData:SavePlayerData(player)
    if not self.Cache[player] then return end
    
    print("PlayerData: Saving data for", player.Name)
    
    local success, error = pcall(function()
        PlayerDataStore:SetAsync(player.UserId, self.Cache[player])
    end)
    
    if success then
        print("PlayerData: Successfully saved data for", player.Name)
    else
        warn("PlayerData: Failed to save data for", player.Name, "-", error)
    end
    
    -- Clear from cache
    self.Cache[player] = nil
end

function PlayerData:SaveAllPlayerData()
    print("PlayerData: Auto-saving all player data...")
    
    for player, _ in pairs(self.Cache) do
        if player.Parent then -- Player still in game
            self:SavePlayerData(player)
            self:LoadPlayerData(player) -- Reload to keep in cache
        end
    end
end

-- Data Access
function PlayerData:GetData(player, key)
    if not self.Cache[player] then return nil end
    
    if key then
        return self.Cache[player][key]
    else
        return self.Cache[player]
    end
end

function PlayerData:SetData(player, key, value)
    if not self.Cache[player] then return false end
    
    self.Cache[player][key] = value
    self.Events.DataUpdated:Fire(player, key, value)
    
    return true
end

-- Currency Management
function PlayerData:GetCash(player)
    return self:GetData(player, "Cash") or 0
end

function PlayerData:AddCash(player, amount)
    local currentCash = self:GetCash(player)
    local newCash = currentCash + amount
    
    self:SetData(player, "Cash", newCash)
    self.Events.CashChanged:Fire(player, currentCash, newCash)
    
    -- Update leaderstats
    if player:FindFirstChild("leaderstats") and player.leaderstats:FindFirstChild("Cash") then
        player.leaderstats.Cash.Value = newCash
    end
    
    print("PlayerData:", player.Name, "gained", amount, "cash. Total:", newCash)
    return true
end

function PlayerData:RemoveCash(player, amount)
    local currentCash = self:GetCash(player)
    
    if currentCash < amount then
        return false -- Not enough cash
    end
    
    local newCash = currentCash - amount
    self:SetData(player, "Cash", newCash)
    self.Events.CashChanged:Fire(player, currentCash, newCash)
    
    -- Update leaderstats
    if player:FindFirstChild("leaderstats") and player.leaderstats:FindFirstChild("Cash") then
        player.leaderstats.Cash.Value = newCash
    end
    
    print("PlayerData:", player.Name, "spent", amount, "cash. Remaining:", newCash)
    return true
end

-- Level/XP Management
function PlayerData:GetLevel(player)
    return self:GetData(player, "Level") or 1
end

function PlayerData:GetXP(player)
    return self:GetData(player, "XP") or 0
end

function PlayerData:AddXP(player, amount)
    local currentXP = self:GetXP(player)
    local currentLevel = self:GetLevel(player)
    local xpToNext = self:GetData(player, "XPToNextLevel") or 100
    
    local newXP = currentXP + amount
    self:SetData(player, "XP", newXP)
    
    -- Check for level up
    if newXP >= xpToNext then
        local newLevel = currentLevel + 1
        local remainingXP = newXP - xpToNext
        local newXPToNext = self:CalculateXPForLevel(newLevel + 1)
        
        self:SetData(player, "Level", newLevel)
        self:SetData(player, "XP", remainingXP)
        self:SetData(player, "XPToNextLevel", newXPToNext)
        
        -- Update leaderstats
        if player:FindFirstChild("leaderstats") and player.leaderstats:FindFirstChild("Level") then
            player.leaderstats.Level.Value = newLevel
        end
        
        self.Events.LevelUp:Fire(player, currentLevel, newLevel)
        print("PlayerData:", player.Name, "leveled up to", newLevel)
    end
    
    print("PlayerData:", player.Name, "gained", amount, "XP")
end

function PlayerData:CalculateXPForLevel(level)
    return 100 + (level - 1) * 50 -- Increasing XP requirement
end

-- Inventory Management
function PlayerData:HasCar(player, carName)
    local ownedCars = self:GetData(player, "OwnedCars") or {}
    
    for _, car in ipairs(ownedCars) do
        if car == carName then
            return true
        end
    end
    
    return false
end

function PlayerData:AddCar(player, carName)
    if self:HasCar(player, carName) then
        return false -- Already owned
    end
    
    local ownedCars = self:GetData(player, "OwnedCars") or {}
    table.insert(ownedCars, carName)
    self:SetData(player, "OwnedCars", ownedCars)
    
    print("PlayerData:", player.Name, "acquired car:", carName)
    return true
end

-- Gamepass Management
function PlayerData:HasGamepass(player, gamepassName)
    local gamepasses = self:GetData(player, "Gamepasses") or {}
    return gamepasses[gamepassName] == true
end

function PlayerData:GrantGamepass(player, gamepassName)
    local gamepasses = self:GetData(player, "Gamepasses") or {}
    gamepasses[gamepassName] = true
    self:SetData(player, "Gamepasses", gamepasses)
    
    print("PlayerData:", player.Name, "granted gamepass:", gamepassName)
end

-- Statistics
function PlayerData:IncrementStat(player, statName)
    local stats = self:GetData(player, "Stats") or {}
    stats[statName] = (stats[statName] or 0) + 1
    self:SetData(player, "Stats", stats)
end

-- Leaderstats
function PlayerData:CreateLeaderstats(player)
    local leaderstats = Instance.new("Folder")
    leaderstats.Name = "leaderstats"
    leaderstats.Parent = player
    
    local cash = Instance.new("IntValue")
    cash.Name = "Cash"
    cash.Value = self:GetCash(player)
    cash.Parent = leaderstats
    
    local level = Instance.new("IntValue")
    level.Name = "Level"
    level.Value = self:GetLevel(player)
    level.Parent = leaderstats
end

-- Utility Functions
function PlayerData:DeepCopy(original)
    local copy = {}
    for key, value in pairs(original) do
        if type(value) == "table" then
            copy[key] = self:DeepCopy(value)
        else
            copy[key] = value
        end
    end
    return copy
end

function PlayerData:MergeData(default, saved)
    local merged = self:DeepCopy(default)
    
    for key, value in pairs(saved) do
        if type(value) == "table" and type(merged[key]) == "table" then
            merged[key] = self:MergeData(merged[key], value)
        else
            merged[key] = value
        end
    end
    
    return merged
end

return PlayerData
