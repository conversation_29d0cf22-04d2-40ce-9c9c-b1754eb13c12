-- MainGui.lua - Main GUI structure for StarterGui
-- Place this script in StarterGui as a LocalScript

-- Services
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Player references
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Wait for remote events
local remotes = ReplicatedStorage:WaitForChild("RemoteEvents")

-- Create the main GUI structure
local function createMainGui()
    -- Main GUI container
    local mainGui = Instance.new("ScreenGui")
    mainGui.Name = "MainGui"
    mainGui.ResetOnSpawn = false
    mainGui.Parent = playerGui
    
    -- Level Frame (as mentioned in ALLTING.MD)
    local levelFrame = Instance.new("Frame")
    levelFrame.Name = "LevelFrameAND"
    levelFrame.Size = UDim2.new(0, 320, 0, 90)
    levelFrame.Position = UDim2.new(0, 15, 0, 15)
    levelFrame.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
    levelFrame.BorderSizePixel = 0
    levelFrame.Parent = mainGui
    
    -- Level frame styling
    local levelCorner = Instance.new("UICorner")
    levelCorner.CornerRadius = UDim.new(0, 10)
    levelCorner.Parent = levelFrame
    
    local levelStroke = Instance.new("UIStroke")
    levelStroke.Color = Color3.fromRGB(0, 150, 255)
    levelStroke.Thickness = 2
    levelStroke.Parent = levelFrame
    
    -- XP Bar Frame (BarXpLevelFRAME as mentioned)
    local barXpLevelFrame = Instance.new("Frame")
    barXpLevelFrame.Name = "BarXpLevelFRAME"
    barXpLevelFrame.Size = UDim2.new(1, -20, 0, 25)
    barXpLevelFrame.Position = UDim2.new(0, 10, 0, 50)
    barXpLevelFrame.BackgroundColor3 = Color3.fromRGB(20, 20, 20)
    barXpLevelFrame.BorderSizePixel = 0
    barXpLevelFrame.Parent = levelFrame
    
    local barCorner = Instance.new("UICorner")
    barCorner.CornerRadius = UDim.new(0, 6)
    barCorner.Parent = barXpLevelFrame
    
    -- XP Bar Fill
    local xpFill = Instance.new("Frame")
    xpFill.Name = "XPFill"
    xpFill.Size = UDim2.new(0.3, 0, 1, 0)
    xpFill.Position = UDim2.new(0, 0, 0, 0)
    xpFill.BackgroundColor3 = Color3.fromRGB(0, 255, 150)
    xpFill.BorderSizePixel = 0
    xpFill.Parent = barXpLevelFrame
    
    local fillCorner = Instance.new("UICorner")
    fillCorner.CornerRadius = UDim.new(0, 6)
    fillCorner.Parent = xpFill
    
    -- XP Level Text (Xp_LevelText as mentioned)
    local xpLevelText = Instance.new("TextLabel")
    xpLevelText.Name = "Xp_LevelText"
    xpLevelText.Size = UDim2.new(1, -20, 0, 35)
    xpLevelText.Position = UDim2.new(0, 10, 0, 8)
    xpLevelText.BackgroundTransparency = 1
    xpLevelText.Text = "Level 1 - 0/100 XP"
    xpLevelText.TextColor3 = Color3.fromRGB(255, 255, 255)
    xpLevelText.TextScaled = true
    xpLevelText.Font = Enum.Font.GothamBold
    xpLevelText.Parent = levelFrame
    
    -- Menu Button (as mentioned in ALLTING.MD)
    local menuButton = Instance.new("TextButton")
    menuButton.Name = "Menu"
    menuButton.Size = UDim2.new(0, 120, 0, 45)
    menuButton.Position = UDim2.new(1, -135, 0, 15)
    menuButton.BackgroundColor3 = Color3.fromRGB(0, 120, 255)
    menuButton.Text = "📋 MENU"
    menuButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    menuButton.TextScaled = true
    menuButton.Font = Enum.Font.GothamBold
    menuButton.BorderSizePixel = 0
    menuButton.Parent = mainGui
    
    local menuCorner = Instance.new("UICorner")
    menuCorner.CornerRadius = UDim.new(0, 10)
    menuCorner.Parent = menuButton
    
    local menuStroke = Instance.new("UIStroke")
    menuStroke.Color = Color3.fromRGB(255, 255, 255)
    menuStroke.Thickness = 1
    menuStroke.Parent = menuButton
    
    -- Sound Buttons Frame (as mentioned in ALLTING.MD)
    local soundButtons = Instance.new("Frame")
    soundButtons.Name = "SoundButtons"
    soundButtons.Size = UDim2.new(0, 140, 0, 45)
    soundButtons.Position = UDim2.new(1, -270, 0, 15)
    soundButtons.BackgroundTransparency = 1
    soundButtons.Parent = mainGui
    
    -- Sound ON Button
    local soundOnButton = Instance.new("ImageButton")
    soundOnButton.Name = "SoundOn"
    soundOnButton.Size = UDim2.new(0, 40, 0, 40)
    soundOnButton.Position = UDim2.new(0, 0, 0, 0)
    soundOnButton.BackgroundColor3 = Color3.fromRGB(0, 200, 0)
    soundOnButton.Image = "rbxasset://textures/ui/VoiceChat/SpeakerLight.png"
    soundOnButton.ImageColor3 = Color3.fromRGB(255, 255, 255)
    soundOnButton.Parent = soundButtons
    
    local soundOnCorner = Instance.new("UICorner")
    soundOnCorner.CornerRadius = UDim.new(0, 8)
    soundOnCorner.Parent = soundOnButton
    
    local soundOnStroke = Instance.new("UIStroke")
    soundOnStroke.Color = Color3.fromRGB(255, 255, 255)
    soundOnStroke.Thickness = 2
    soundOnStroke.Parent = soundOnButton
    
    -- Sound OFF Button
    local soundOffButton = Instance.new("ImageButton")
    soundOffButton.Name = "SoundOff"
    soundOffButton.Size = UDim2.new(0, 40, 0, 40)
    soundOffButton.Position = UDim2.new(0, 50, 0, 0)
    soundOffButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
    soundOffButton.Image = "rbxasset://textures/ui/VoiceChat/MicMutedLight.png"
    soundOffButton.ImageColor3 = Color3.fromRGB(255, 255, 255)
    soundOffButton.Parent = soundButtons
    
    local soundOffCorner = Instance.new("UICorner")
    soundOffCorner.CornerRadius = UDim.new(0, 8)
    soundOffCorner.Parent = soundOffButton
    
    local soundOffStroke = Instance.new("UIStroke")
    soundOffStroke.Color = Color3.fromRGB(150, 150, 150)
    soundOffStroke.Thickness = 2
    soundOffStroke.Parent = soundOffButton
    
    -- Cash Display
    local cashFrame = Instance.new("Frame")
    cashFrame.Name = "CashFrame"
    cashFrame.Size = UDim2.new(0, 200, 0, 50)
    cashFrame.Position = UDim2.new(0, 15, 0, 115)
    cashFrame.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
    cashFrame.BorderSizePixel = 0
    cashFrame.Parent = mainGui
    
    local cashCorner = Instance.new("UICorner")
    cashCorner.CornerRadius = UDim.new(0, 10)
    cashCorner.Parent = cashFrame
    
    local cashStroke = Instance.new("UIStroke")
    cashStroke.Color = Color3.fromRGB(0, 255, 100)
    cashStroke.Thickness = 2
    cashStroke.Parent = cashFrame
    
    local cashText = Instance.new("TextLabel")
    cashText.Name = "CashText"
    cashText.Size = UDim2.new(1, -20, 1, -10)
    cashText.Position = UDim2.new(0, 10, 0, 5)
    cashText.BackgroundTransparency = 1
    cashText.Text = "💰 $500"
    cashText.TextColor3 = Color3.fromRGB(0, 255, 100)
    cashText.TextScaled = true
    cashText.Font = Enum.Font.GothamBold
    cashText.Parent = cashFrame
    
    -- Game Timer Display
    local timerFrame = Instance.new("Frame")
    timerFrame.Name = "TimerFrame"
    timerFrame.Size = UDim2.new(0, 250, 0, 70)
    timerFrame.Position = UDim2.new(0.5, -125, 0, 15)
    timerFrame.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
    timerFrame.BorderSizePixel = 0
    timerFrame.Parent = mainGui
    
    local timerCorner = Instance.new("UICorner")
    timerCorner.CornerRadius = UDim.new(0, 10)
    timerCorner.Parent = timerFrame
    
    local timerStroke = Instance.new("UIStroke")
    timerStroke.Color = Color3.fromRGB(255, 200, 0)
    timerStroke.Thickness = 2
    timerStroke.Parent = timerFrame
    
    local timerText = Instance.new("TextLabel")
    timerText.Name = "TimerText"
    timerText.Size = UDim2.new(1, -20, 1, -20)
    timerText.Position = UDim2.new(0, 10, 0, 10)
    timerText.BackgroundTransparency = 1
    timerText.Text = "⏰ Waiting for players..."
    timerText.TextColor3 = Color3.fromRGB(255, 255, 255)
    timerText.TextScaled = true
    timerText.Font = Enum.Font.GothamBold
    timerText.Parent = timerFrame
    
    print("MainGui: Created main GUI structure")
    return mainGui, menuButton, soundOnButton, soundOffButton
end

-- Create team selection menu (Change Teams as mentioned in ALLTING.MD)
local function createTeamMenu()
    local teamGui = Instance.new("ScreenGui")
    teamGui.Name = "TeamMenu"
    teamGui.ResetOnSpawn = false
    teamGui.Enabled = false
    teamGui.Parent = playerGui
    
    -- Background blur (using TextButton for click detection)
    local blurFrame = Instance.new("TextButton")
    blurFrame.Name = "BlurBackground"
    blurFrame.Size = UDim2.new(1, 0, 1, 0)
    blurFrame.Position = UDim2.new(0, 0, 0, 0)
    blurFrame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    blurFrame.BackgroundTransparency = 0.5
    blurFrame.BorderSizePixel = 0
    blurFrame.Text = "" -- Empty text
    blurFrame.TextTransparency = 1 -- Invisible text
    blurFrame.Parent = teamGui
    
    -- Main menu frame
    local menuFrame = Instance.new("Frame")
    menuFrame.Name = "MenuFrame"
    menuFrame.Size = UDim2.new(0, 600, 0, 500)
    menuFrame.Position = UDim2.new(0.5, -300, 0.5, -250)
    menuFrame.BackgroundColor3 = Color3.fromRGB(25, 25, 25)
    menuFrame.BorderSizePixel = 0
    menuFrame.Parent = teamGui
    
    local menuCorner = Instance.new("UICorner")
    menuCorner.CornerRadius = UDim.new(0, 15)
    menuCorner.Parent = menuFrame
    
    local menuStroke = Instance.new("UIStroke")
    menuStroke.Color = Color3.fromRGB(0, 150, 255)
    menuStroke.Thickness = 3
    menuStroke.Parent = menuFrame
    
    -- Title
    local titleText = Instance.new("TextLabel")
    titleText.Name = "TitleText"
    titleText.Size = UDim2.new(1, -40, 0, 60)
    titleText.Position = UDim2.new(0, 20, 0, 20)
    titleText.BackgroundTransparency = 1
    titleText.Text = "🎮 GAME MENU"
    titleText.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleText.TextScaled = true
    titleText.Font = Enum.Font.GothamBold
    titleText.Parent = menuFrame
    
    -- Team Selection Section
    local teamSection = Instance.new("Frame")
    teamSection.Name = "TeamSection"
    teamSection.Size = UDim2.new(1, -40, 0, 150)
    teamSection.Position = UDim2.new(0, 20, 0, 90)
    teamSection.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    teamSection.BorderSizePixel = 0
    teamSection.Parent = menuFrame
    
    local teamCorner = Instance.new("UICorner")
    teamCorner.CornerRadius = UDim.new(0, 10)
    teamCorner.Parent = teamSection
    
    local teamTitle = Instance.new("TextLabel")
    teamTitle.Name = "TeamTitle"
    teamTitle.Size = UDim2.new(1, -20, 0, 30)
    teamTitle.Position = UDim2.new(0, 10, 0, 10)
    teamTitle.BackgroundTransparency = 1
    teamTitle.Text = "👥 TEAM PREFERENCES"
    teamTitle.TextColor3 = Color3.fromRGB(200, 200, 200)
    teamTitle.TextScaled = true
    teamTitle.Font = Enum.Font.Gotham
    teamTitle.Parent = teamSection
    
    -- Team buttons
    local teams = {
        {name = "Any", color = Color3.fromRGB(100, 100, 100), icon = "🎲"},
        {name = "Criminal", color = Color3.fromRGB(200, 50, 50), icon = "🔫"},
        {name = "Police", color = Color3.fromRGB(50, 50, 200), icon = "👮"},
        {name = "Citizen", color = Color3.fromRGB(50, 200, 50), icon = "👤"}
    }
    
    for i, team in ipairs(teams) do
        local teamButton = Instance.new("TextButton")
        teamButton.Name = team.name .. "Button"
        teamButton.Size = UDim2.new(0.22, 0, 0, 60)
        teamButton.Position = UDim2.new((i-1) * 0.25 + 0.02, 0, 0, 70)
        teamButton.BackgroundColor3 = team.color
        teamButton.Text = team.icon .. "\n" .. team.name
        teamButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        teamButton.TextScaled = true
        teamButton.Font = Enum.Font.GothamBold
        teamButton.BorderSizePixel = 0
        teamButton.Parent = teamSection
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 8)
        buttonCorner.Parent = teamButton
        
        teamButton.MouseButton1Click:Connect(function()
            print("Team preference set to:", team.name)

            -- Update visual feedback - highlight selected button
            for _, otherButton in pairs(teamSection:GetChildren()) do
                if otherButton:IsA("TextButton") and otherButton.Name:find("Button") then
                    otherButton.BackgroundTransparency = 0
                end
            end
            teamButton.BackgroundTransparency = 0.3 -- Highlight selected

            -- Here you would send the preference to the server
            -- Example: remotes.SetTeamPreference:FireServer(team.name)
        end)
    end
    
    -- Car Shop Button
    local carShopButton = Instance.new("TextButton")
    carShopButton.Name = "CarShopButton"
    carShopButton.Size = UDim2.new(1, -40, 0, 60)
    carShopButton.Position = UDim2.new(0, 20, 0, 260)
    carShopButton.BackgroundColor3 = Color3.fromRGB(255, 150, 0)
    carShopButton.Text = "🚗 CAR SHOP"
    carShopButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    carShopButton.TextScaled = true
    carShopButton.Font = Enum.Font.GothamBold
    carShopButton.BorderSizePixel = 0
    carShopButton.Parent = menuFrame
    
    local carShopCorner = Instance.new("UICorner")
    carShopCorner.CornerRadius = UDim.new(0, 10)
    carShopCorner.Parent = carShopButton
    
    -- Settings Button
    local settingsButton = Instance.new("TextButton")
    settingsButton.Name = "SettingsButton"
    settingsButton.Size = UDim2.new(0.48, 0, 0, 50)
    settingsButton.Position = UDim2.new(0, 20, 0, 340)
    settingsButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
    settingsButton.Text = "⚙️ SETTINGS"
    settingsButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    settingsButton.TextScaled = true
    settingsButton.Font = Enum.Font.GothamBold
    settingsButton.BorderSizePixel = 0
    settingsButton.Parent = menuFrame
    
    local settingsCorner = Instance.new("UICorner")
    settingsCorner.CornerRadius = UDim.new(0, 10)
    settingsCorner.Parent = settingsButton
    
    -- Gamepasses Button
    local gamepassButton = Instance.new("TextButton")
    gamepassButton.Name = "GamepassButton"
    gamepassButton.Size = UDim2.new(0.48, 0, 0, 50)
    gamepassButton.Position = UDim2.new(0.52, 0, 0, 340)
    gamepassButton.BackgroundColor3 = Color3.fromRGB(255, 200, 0)
    gamepassButton.Text = "💎 GAMEPASSES"
    gamepassButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    gamepassButton.TextScaled = true
    gamepassButton.Font = Enum.Font.GothamBold
    gamepassButton.BorderSizePixel = 0
    gamepassButton.Parent = menuFrame
    
    local gamepassCorner = Instance.new("UICorner")
    gamepassCorner.CornerRadius = UDim.new(0, 10)
    gamepassCorner.Parent = gamepassButton
    
    -- Close Button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(1, -40, 0, 50)
    closeButton.Position = UDim2.new(0, 20, 0, 410)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "❌ CLOSE"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.GothamBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = menuFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 10)
    closeCorner.Parent = closeButton
    
    -- Connect buttons
    carShopButton.MouseButton1Click:Connect(function()
        if _G.CarShopClient then
            _G.CarShopClient.OpenShop()
        end
        teamGui.Enabled = false
    end)
    
    closeButton.MouseButton1Click:Connect(function()
        teamGui.Enabled = false
    end)

    -- Now we can use MouseButton1Click since it's a TextButton
    blurFrame.MouseButton1Click:Connect(function()
        teamGui.Enabled = false
    end)
    
    print("MainGui: Created team menu")
    return teamGui
end

-- Initialize the GUI system
local function initialize()
    print("MainGui: Initializing StartGui system...")
    
    -- Create main GUI
    local mainGui, menuButton, soundOnButton, soundOffButton = createMainGui()
    
    -- Create team menu
    local teamMenu = createTeamMenu()
    
    -- Connect menu button
    menuButton.MouseButton1Click:Connect(function()
        teamMenu.Enabled = not teamMenu.Enabled
        
        if teamMenu.Enabled then
            -- Animate menu opening
            local menuFrame = teamMenu.MenuFrame
            menuFrame.Size = UDim2.new(0, 0, 0, 0)
            menuFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
            
            local openTween = TweenService:Create(menuFrame,
                TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
                {
                    Size = UDim2.new(0, 600, 0, 500),
                    Position = UDim2.new(0.5, -300, 0.5, -250)
                }
            )
            openTween:Play()
        end
    end)
    
    -- Connect sound buttons
    local soundEnabled = true
    
    soundOnButton.MouseButton1Click:Connect(function()
        soundEnabled = true
        soundOnButton.BackgroundColor3 = Color3.fromRGB(0, 200, 0)
        soundOffButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
        
        -- Send to server
        local soundToggle = remotes:WaitForChild("SoundToggle")
        soundToggle:InvokeServer("Music", true)
    end)
    
    soundOffButton.MouseButton1Click:Connect(function()
        soundEnabled = false
        soundOnButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
        soundOffButton.BackgroundColor3 = Color3.fromRGB(200, 0, 0)
        
        -- Send to server
        local soundToggle = remotes:WaitForChild("SoundToggle")
        soundToggle:InvokeServer("Music", false)
    end)
    
    -- Connect to remote events for updates
    local playerDataUpdated = remotes:WaitForChild("PlayerDataUpdated")
    playerDataUpdated.OnClientEvent:Connect(function(key, value)
        if key == "Cash" then
            local cashText = mainGui:FindFirstChild("CashFrame"):FindFirstChild("CashText")
            if cashText then
                cashText.Text = "💰 $" .. value
            end
        elseif key == "Level" or key == "XP" or key == "XPToNextLevel" then
            -- Update level display (this would need more data)
        end
    end)
    
    local roundTimer = remotes:WaitForChild("RoundTimer")
    roundTimer.OnClientEvent:Connect(function(timeRemaining, gameState)
        local timerText = mainGui:FindFirstChild("TimerFrame"):FindFirstChild("TimerText")
        if timerText then
            if gameState == "Waiting" then
                timerText.Text = "⏰ Waiting for players..."
            elseif gameState == "Starting" then
                timerText.Text = "⏰ Starting in " .. math.ceil(timeRemaining)
            elseif gameState == "Playing" then
                local minutes = math.floor(timeRemaining / 60)
                local seconds = math.floor(timeRemaining % 60)
                timerText.Text = string.format("⏰ %02d:%02d", minutes, seconds)
            elseif gameState == "Ending" then
                timerText.Text = "⏰ Round ended!"
            end
        end
    end)
    
    print("MainGui: StartGui system initialized successfully!")
end

-- Start initialization
initialize()
