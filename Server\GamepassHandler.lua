-- GamepassHandler.lua - Handles gamepass purchases and benefits
-- Place this script in ServerScriptService

-- Services
local Players = game:GetService("Players")
local MarketplaceService = game:GetService("MarketplaceService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Gamepass IDs (Replace with your actual gamepass IDs from Roblox)
local GAMEPASS_IDS = {
    VIPPass = 0, -- Replace with actual ID
    SupercarPack = 0, -- Replace with actual ID
    SWATPack = 0, -- Replace with actual ID
    PrisonEscapePass = 0, -- Replace with actual ID
    GarageExpansion = 0, -- Replace with actual ID
    LuxuryCarsPack = 0 -- Replace with actual ID
}

-- Load PlayerData module
local PlayerData = require(script.Parent.Parent:FindFirstChild("ModulesScript"):FindFirstChild("PlayerData"))

-- Gamepass benefits configuration
local GAMEPASS_BENEFITS = {
    VIPPass = {
        CashMultiplier = 1.25, -- 25% more cash
        XPMultiplier = 1.25, -- 25% more XP
        SpecialTitle = "VIP",
        ExclusiveSkins = {"VIPGlasses", "GoldenHandcuffs", "VIPMask"},
        Description = "Get 25% more cash and XP, plus exclusive VIP items!"
    },
    
    SupercarPack = {
        UnlockedCars = {"Supercar", "HyperCar", "RaceCar"},
        Description = "Unlock exclusive supercars not available in the regular shop!"
    },
    
    SWATPack = {
        UnlockedCars = {"SWATVehicle", "ArmoredTruck", "TacticalVan"},
        PoliceBonus = true,
        Description = "Access to armored SWAT vehicles and enhanced police equipment!"
    },
    
    PrisonEscapePass = {
        InstantEscape = true,
        EscapeBonus = 1.5, -- 50% more reward for escaping
        Description = "Instantly escape from prison once per round!"
    },
    
    GarageExpansion = {
        MaxCars = 20, -- Instead of default 10
        Description = "Double your garage space to own more cars!"
    },
    
    LuxuryCarsPack = {
        UnlockedCars = {"LuxuryCar", "SportsCar", "ConvertibleCar"},
        CustomPaints = {"Gold", "Chrome", "Neon"},
        Description = "Access to luxury vehicles and premium paint options!"
    }
}

-- Check if player owns a gamepass
local function checkGamepassOwnership(player, gamepassName)
    local gamepassId = GAMEPASS_IDS[gamepassName]
    if gamepassId == 0 then
        warn("GamepassHandler: Gamepass ID not set for", gamepassName)
        return false
    end
    
    local success, ownsGamepass = pcall(function()
        return MarketplaceService:UserOwnsGamePassAsync(player.UserId, gamepassId)
    end)
    
    if success then
        return ownsGamepass
    else
        warn("GamepassHandler: Failed to check gamepass ownership for", player.Name, gamepassName)
        return false
    end
end

-- Grant gamepass benefits to player
local function grantGamepassBenefits(player, gamepassName)
    local benefits = GAMEPASS_BENEFITS[gamepassName]
    if not benefits then
        warn("GamepassHandler: No benefits defined for gamepass", gamepassName)
        return
    end
    
    print("GamepassHandler: Granting", gamepassName, "benefits to", player.Name)
    
    -- Mark gamepass as owned in player data
    PlayerData:GrantGamepass(player, gamepassName)
    
    -- Grant specific benefits
    if benefits.UnlockedCars then
        for _, carName in ipairs(benefits.UnlockedCars) do
            PlayerData:AddCar(player, carName)
            print("GamepassHandler: Granted car", carName, "to", player.Name)
        end
    end
    
    if benefits.ExclusiveSkins then
        local ownedSkins = PlayerData:GetData(player, "OwnedSkins") or {}
        for _, skinName in ipairs(benefits.ExclusiveSkins) do
            if not table.find(ownedSkins, skinName) then
                table.insert(ownedSkins, skinName)
            end
        end
        PlayerData:SetData(player, "OwnedSkins", ownedSkins)
        print("GamepassHandler: Granted exclusive skins to", player.Name)
    end
    
    if benefits.CustomPaints then
        local ownedAccessories = PlayerData:GetData(player, "OwnedAccessories") or {}
        for _, paintName in ipairs(benefits.CustomPaints) do
            if not table.find(ownedAccessories, paintName) then
                table.insert(ownedAccessories, paintName)
            end
        end
        PlayerData:SetData(player, "OwnedAccessories", ownedAccessories)
        print("GamepassHandler: Granted custom paints to", player.Name)
    end
    
    -- Send notification to player
    local remotes = ReplicatedStorage:FindFirstChild("RemoteEvents")
    if remotes then
        local gamepassGranted = remotes:FindFirstChild("GamepassGranted")
        if gamepassGranted then
            gamepassGranted:FireClient(player, gamepassName, benefits.Description)
        end
    end
end

-- Check all gamepasses for a player
local function checkAllGamepasses(player)
    print("GamepassHandler: Checking gamepasses for", player.Name)
    
    for gamepassName, gamepassId in pairs(GAMEPASS_IDS) do
        if gamepassId ~= 0 then
            local ownsGamepass = checkGamepassOwnership(player, gamepassName)
            local hasInData = PlayerData:HasGamepass(player, gamepassName)
            
            if ownsGamepass and not hasInData then
                grantGamepassBenefits(player, gamepassName)
            end
        end
    end
end

-- Apply gamepass multipliers to rewards
local function applyGamepassMultipliers(player, cashAmount, xpAmount)
    local finalCash = cashAmount
    local finalXP = xpAmount
    
    -- VIP Pass multipliers
    if PlayerData:HasGamepass(player, "VIPPass") then
        local benefits = GAMEPASS_BENEFITS.VIPPass
        finalCash = finalCash * benefits.CashMultiplier
        finalXP = finalXP * benefits.XPMultiplier
    end
    
    return math.floor(finalCash), math.floor(finalXP)
end

-- Handle gamepass purchase
local function onGamepassPurchased(player, gamepassId, wasPurchased)
    if not wasPurchased then return end
    
    -- Find which gamepass was purchased
    local purchasedGamepass = nil
    for gamepassName, id in pairs(GAMEPASS_IDS) do
        if id == gamepassId then
            purchasedGamepass = gamepassName
            break
        end
    end
    
    if purchasedGamepass then
        print("GamepassHandler:", player.Name, "purchased", purchasedGamepass)
        grantGamepassBenefits(player, purchasedGamepass)
    else
        warn("GamepassHandler: Unknown gamepass purchased with ID", gamepassId)
    end
end

-- Initialize gamepass system
local function initialize()
    print("GamepassHandler: Initializing...")
    
    -- Create gamepass notification remote event
    local remotes = ReplicatedStorage:FindFirstChild("RemoteEvents")
    if remotes then
        local gamepassGranted = Instance.new("RemoteEvent")
        gamepassGranted.Name = "GamepassGranted"
        gamepassGranted.Parent = remotes
    end
    
    -- Connect marketplace events
    MarketplaceService.PromptGamePassPurchaseFinished:Connect(onGamepassPurchased)
    
    -- Check gamepasses for existing players
    for _, player in ipairs(Players:GetPlayers()) do
        spawn(function()
            -- Wait for player data to load
            while not PlayerData:GetData(player) do
                wait(0.1)
            end
            checkAllGamepasses(player)
        end)
    end
    
    -- Check gamepasses for new players
    Players.PlayerAdded:Connect(function(player)
        spawn(function()
            -- Wait for player data to load
            while not PlayerData:GetData(player) do
                wait(0.1)
            end
            checkAllGamepasses(player)
        end)
    end)
    
    print("GamepassHandler: Initialized successfully!")
end

-- Public functions for other scripts to use
local GamepassHandler = {}

function GamepassHandler:ApplyMultipliers(player, cash, xp)
    return applyGamepassMultipliers(player, cash, xp)
end

function GamepassHandler:CheckGamepass(player, gamepassName)
    return PlayerData:HasGamepass(player, gamepassName)
end

function GamepassHandler:GetGamepassBenefits(gamepassName)
    return GAMEPASS_BENEFITS[gamepassName]
end

function GamepassHandler:PromptPurchase(player, gamepassName)
    local gamepassId = GAMEPASS_IDS[gamepassName]
    if gamepassId ~= 0 then
        MarketplaceService:PromptGamePassPurchase(player, gamepassId)
        return true
    else
        warn("GamepassHandler: Gamepass ID not set for", gamepassName)
        return false
    end
end

-- Initialize the system
initialize()

-- Return the module for other scripts to use
return GamepassHandler
