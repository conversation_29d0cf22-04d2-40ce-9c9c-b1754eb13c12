-- GameManager.lua - Core game management module
local GameManager = {}

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

-- Game States
GameManager.GameState = {
    WAITING = "Waiting",
    STARTING = "Starting", 
    PLAYING = "Playing",
    ENDING = "Ending"
}

-- Game Settings
GameManager.Settings = {
    MIN_PLAYERS = 3,
    MAX_PLAYERS = 12,
    ROUND_TIME = 300, -- 5 minutes
    INTERMISSION_TIME = 15,
    CRIMINAL_COUNT = 1,
    POLICE_COUNT = 2
}

-- Game Variables
GameManager.CurrentState = GameManager.GameState.WAITING
GameManager.RoundTimer = 0
GameManager.PlayersInGame = {}
GameManager.CurrentRoles = {}

-- Events
GameManager.Events = {
    StateChanged = Instance.new("BindableEvent"),
    RoundStarted = Instance.new("BindableEvent"),
    RoundEnded = Instance.new("BindableEvent"),
    PlayerRoleAssigned = Instance.new("BindableEvent")
}

-- Initialize Game Manager
function GameManager:Initialize()
    print("GameManager: Initializing...")
    
    -- Connect player events
    Players.PlayerAdded:Connect(function(player)
        self:OnPlayerJoined(player)
    end)
    
    Players.PlayerRemoving:Connect(function(player)
        self:OnPlayerLeft(player)
    end)
    
    -- Start game loop
    self:StartGameLoop()
    
    print("GameManager: Initialized successfully!")
end

-- Player Management
function GameManager:OnPlayerJoined(player)
    print("GameManager: Player joined -", player.Name)
    
    table.insert(self.PlayersInGame, player)
    
    -- Check if we can start the game
    if #self.PlayersInGame >= self.Settings.MIN_PLAYERS and self.CurrentState == self.GameState.WAITING then
        self:StartIntermission()
    end
end

function GameManager:OnPlayerLeft(player)
    print("GameManager: Player left -", player.Name)
    
    -- Remove from players list
    for i, p in ipairs(self.PlayersInGame) do
        if p == player then
            table.remove(self.PlayersInGame, i)
            break
        end
    end
    
    -- Remove role assignment
    self.CurrentRoles[player] = nil
    
    -- Check if game should end
    if #self.PlayersInGame < self.Settings.MIN_PLAYERS and self.CurrentState == self.GameState.PLAYING then
        self:EndRound("Not enough players")
    end
end

-- Game State Management
function GameManager:ChangeState(newState)
    local oldState = self.CurrentState
    self.CurrentState = newState
    
    print("GameManager: State changed from", oldState, "to", newState)
    self.Events.StateChanged:Fire(oldState, newState)
end

function GameManager:StartIntermission()
    self:ChangeState(self.GameState.STARTING)
    self.RoundTimer = self.Settings.INTERMISSION_TIME
    
    print("GameManager: Starting intermission...")
end

function GameManager:StartRound()
    self:ChangeState(self.GameState.PLAYING)
    self.RoundTimer = self.Settings.ROUND_TIME
    
    -- Assign roles
    self:AssignRoles()
    
    print("GameManager: Round started!")
    self.Events.RoundStarted:Fire()
end

function GameManager:EndRound(reason)
    self:ChangeState(self.GameState.ENDING)
    
    print("GameManager: Round ended -", reason or "Time up")
    self.Events.RoundEnded:Fire(reason)
    
    -- Clear roles
    self.CurrentRoles = {}
    
    -- Wait before next round
    wait(5)
    
    if #self.PlayersInGame >= self.Settings.MIN_PLAYERS then
        self:StartIntermission()
    else
        self:ChangeState(self.GameState.WAITING)
    end
end

-- Role Assignment
function GameManager:AssignRoles()
    local availablePlayers = {}
    for _, player in ipairs(self.PlayersInGame) do
        table.insert(availablePlayers, player)
    end
    
    -- Shuffle players
    for i = #availablePlayers, 2, -1 do
        local j = math.random(i)
        availablePlayers[i], availablePlayers[j] = availablePlayers[j], availablePlayers[i]
    end
    
    -- Assign criminal
    local criminal = availablePlayers[1]
    self.CurrentRoles[criminal] = "Criminal"
    table.remove(availablePlayers, 1)
    
    -- Assign police
    local policeCount = math.min(self.Settings.POLICE_COUNT, #availablePlayers)
    for i = 1, policeCount do
        self.CurrentRoles[availablePlayers[i]] = "Police"
    end
    
    -- Remaining are citizens
    for i = policeCount + 1, #availablePlayers do
        self.CurrentRoles[availablePlayers[i]] = "Citizen"
    end
    
    -- Fire role assignment events
    for player, role in pairs(self.CurrentRoles) do
        self.Events.PlayerRoleAssigned:Fire(player, role)
        print("GameManager: Assigned", role, "to", player.Name)
    end
end

-- Game Loop
function GameManager:StartGameLoop()
    RunService.Heartbeat:Connect(function()
        self:UpdateGameLoop()
    end)
end

function GameManager:UpdateGameLoop()
    if self.CurrentState == self.GameState.STARTING then
        self.RoundTimer = self.RoundTimer - RunService.Heartbeat:Wait()
        
        if self.RoundTimer <= 0 then
            self:StartRound()
        end
        
    elseif self.CurrentState == self.GameState.PLAYING then
        self.RoundTimer = self.RoundTimer - RunService.Heartbeat:Wait()
        
        if self.RoundTimer <= 0 then
            self:EndRound("Time up")
        end
    end
end

-- Utility Functions
function GameManager:GetPlayerRole(player)
    return self.CurrentRoles[player] or "Spectator"
end

function GameManager:GetPlayersWithRole(role)
    local players = {}
    for player, playerRole in pairs(self.CurrentRoles) do
        if playerRole == role then
            table.insert(players, player)
        end
    end
    return players
end

function GameManager:GetTimeRemaining()
    return math.max(0, self.RoundTimer)
end

return GameManager
