# 🔧 Troubleshooting Guide - MainGui Issues

## ❌ Common Error: "Infinite yield possible on PlayerGui.MainGui:WaitF<PERSON><PERSON>hil<PERSON>('Menu')"

### 🔍 **What This Error Means:**
This error occurs when the client script (`Clients/MainClient.lua`) is trying to find the MainGui created by the StarterGui script, but it can't find it within the timeout period.

### 🎯 **Root Causes:**

1. **StarterGui script not placed correctly**
2. **StarterGui script has errors and isn't running**
3. **Timing issues - client script runs before StarterGui script**
4. **Script placement order issues**

### ✅ **Step-by-Step Fix:**

#### **Step 1: Verify Script Placement**
```
StarterGui/
└── MainGui (LocalScript) ← StartGui/MainGui.lua goes here

StarterPlayer/
└── StarterPlayerScripts/
    ├── MainClient (LocalScript) ← Clients/MainClient.lua goes here
    └── CarShopClient (LocalScript) ← Clients/CarShopClient.lua goes here
```

#### **Step 2: Check StarterGui Script**
1. Open **StarterGui** in Roblox Studio
2. Make sure there's a **LocalScript** named "MainGui"
3. Check the **Output** window for any errors from the StarterGui script
4. The script should print: `"MainGui: StartGui system initialized successfully!"`

#### **Step 3: Check Script Execution Order**
1. In **Output** window, you should see:
   ```
   MainGui: Initializing StartGui system...
   MainGui: Created main GUI structure
   MainGui: Created team menu
   MainGui: StartGui system initialized successfully!
   MainClient: Initializing...
   MainClient: Waiting for MainGui...
   MainClient: MainGui found! Children: [list of children]
   MainClient: Connected to existing MainGui successfully
   ```

#### **Step 4: Manual Debug**
If the error persists, add this debug script to **ServerScriptService**:

```lua
-- DebugMainGui.lua - Place in ServerScriptService
local Players = game:GetService("Players")

Players.PlayerAdded:Connect(function(player)
    player.CharacterAdded:Connect(function()
        wait(2) -- Wait for GUIs to load
        
        local playerGui = player:WaitForChild("PlayerGui")
        print("=== DEBUG INFO FOR", player.Name, "===")
        print("PlayerGui children:")
        for _, gui in pairs(playerGui:GetChildren()) do
            print("  -", gui.Name, "(" .. gui.ClassName .. ")")
            
            if gui.Name == "MainGui" then
                print("    MainGui children:")
                for _, child in pairs(gui:GetChildren()) do
                    print("      -", child.Name, "(" .. child.ClassName .. ")")
                end
            end
        end
        print("=== END DEBUG INFO ===")
    end)
end)
```

### 🔧 **Alternative Solutions:**

#### **Solution A: Single Script Approach**
If the dual-script approach keeps failing, combine everything into one script:

1. **Delete** `Clients/MainClient.lua`
2. **Move all functionality** from MainClient into `StartGui/MainGui.lua`
3. **Place the combined script** in StarterGui

#### **Solution B: Fallback GUI Creation**
Update `Clients/MainClient.lua` to create its own GUI if StarterGui fails:

```lua
-- Add this function to MainClient.lua
local function createFallbackGui()
    warn("MainClient: Creating fallback GUI since StarterGui failed")
    
    -- Create basic MainGui
    mainGui = Instance.new("ScreenGui")
    mainGui.Name = "MainGui_Fallback"
    mainGui.ResetOnSpawn = false
    mainGui.Parent = playerGui
    
    -- Create basic menu button
    local menuButton = Instance.new("TextButton")
    menuButton.Name = "Menu"
    menuButton.Size = UDim2.new(0, 100, 0, 40)
    menuButton.Position = UDim2.new(1, -110, 0, 10)
    menuButton.BackgroundColor3 = Color3.fromRGB(0, 120, 255)
    menuButton.Text = "MENU"
    menuButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    menuButton.TextScaled = true
    menuButton.Font = Enum.Font.GothamBold
    menuButton.Parent = mainGui
    
    -- Connect menu button
    menuButton.MouseButton1Click:Connect(function()
        toggleMenu()
    end)
    
    print("MainClient: Fallback GUI created")
    return true
end

-- Update the getMainGui function
local function getMainGui()
    print("MainClient: Waiting for MainGui...")
    
    mainGui = playerGui:WaitForChild("MainGui", 10)
    
    if not mainGui then
        warn("MainClient: MainGui not found! Creating fallback...")
        return createFallbackGui()
    end
    
    -- Rest of the existing code...
end
```

### 🎯 **Quick Test:**

1. **Place this test script** in StarterPlayerScripts:
```lua
-- TestMainGui.lua - Quick test script
local Players = game:GetService("Players")
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

wait(3) -- Wait for everything to load

print("=== QUICK TEST ===")
local mainGui = playerGui:FindFirstChild("MainGui")
if mainGui then
    print("✅ MainGui found!")
    local menu = mainGui:FindFirstChild("Menu")
    if menu then
        print("✅ Menu button found!")
    else
        print("❌ Menu button NOT found!")
    end
else
    print("❌ MainGui NOT found!")
    print("Available GUIs:", table.concat(playerGui:GetChildren(), ", "))
end
print("=== END TEST ===")
```

2. **Run the game** and check Output
3. **Look for the test results** to identify the exact issue

### 📋 **Prevention Checklist:**

- [ ] StarterGui/MainGui.lua is placed as LocalScript in StarterGui
- [ ] Clients/MainClient.lua is placed as LocalScript in StarterPlayerScripts  
- [ ] No errors in Output window from either script
- [ ] StarterGui script runs and prints success message
- [ ] Client script waits long enough for StarterGui to complete
- [ ] All required services are available

### 🆘 **If All Else Fails:**

**Use the single-script approach:**
1. Copy all code from `Clients/MainClient.lua`
2. Paste it at the end of `StartGui/MainGui.lua`
3. Remove the `getMainGui()` function calls
4. Delete `Clients/MainClient.lua`
5. Everything will run from one script in StarterGui

This eliminates timing issues and ensures everything works together! 🎮
