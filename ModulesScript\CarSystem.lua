-- CarSystem.lua - Vehicle management module
local CarSystem = {}

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local TweenService = game:GetService("TweenService")

-- Car configurations
CarSystem.CarConfigs = {
    -- Civilian Cars
    BasicCar = {
        Name = "Basic Car",
        Price = 0,
        Speed = 50,
        Handling = 60,
        Category = "Civilian",
        Model = "BasicCarModel",
        RequiredLevel = 1,
        Description = "A simple starter car for getting around the city."
    },
    
    FamilyCar = {
        Name = "Family Car",
        Price = 1500,
        Speed = 55,
        Handling = 65,
        Category = "Civilian",
        Model = "FamilyCarModel",
        RequiredLevel = 3,
        Description = "Reliable family vehicle with good comfort."
    },
    
    SportsCar = {
        Name = "Sports Car",
        Price = 5000,
        Speed = 80,
        Handling = 85,
        Category = "Civilian",
        Model = "SportsCarModel",
        RequiredLevel = 10,
        Description = "Fast and stylish sports car for racing enthusiasts."
    },
    
    -- Police Cars
    PoliceCruiser = {
        Name = "Police Cruiser",
        Price = 0,
        Speed = 70,
        Handling = 75,
        Category = "Police",
        Model = "PoliceCruiserModel",
        RequiredLevel = 1,
        Description = "Standard police vehicle for patrol duties.",
        RoleRequired = "Police"
    },
    
    SWATVehicle = {
        Name = "SWAT Vehicle",
        Price = 0,
        Speed = 65,
        Handling = 70,
        Category = "Police",
        Model = "SWATVehicleModel",
        RequiredLevel = 5,
        Description = "Armored vehicle for high-risk operations.",
        RoleRequired = "Police",
        GamepassRequired = "SWATPack"
    },
    
    -- Criminal Cars
    GetawayCar = {
        Name = "Getaway Car",
        Price = 3000,
        Speed = 85,
        Handling = 80,
        Category = "Criminal",
        Model = "GetawayCarModel",
        RequiredLevel = 5,
        Description = "Fast car perfect for quick escapes."
    },
    
    Motorcycle = {
        Name = "Motorcycle",
        Price = 2000,
        Speed = 90,
        Handling = 95,
        Category = "Criminal",
        Model = "MotorcycleModel",
        RequiredLevel = 7,
        Description = "Agile bike for weaving through traffic."
    },
    
    -- Special Cars (Gamepass)
    Supercar = {
        Name = "Supercar",
        Price = 0,
        Speed = 100,
        Handling = 90,
        Category = "Special",
        Model = "SupercarModel",
        RequiredLevel = 1,
        Description = "Ultimate speed machine for VIP players.",
        GamepassRequired = "SupercarPack"
    },
    
    LuxuryCar = {
        Name = "Luxury Car",
        Price = 8000,
        Speed = 75,
        Handling = 80,
        Category = "Special",
        Model = "LuxuryCarModel",
        RequiredLevel = 15,
        Description = "Elegant luxury vehicle for style and comfort."
    }
}

-- Spawn points for different car types
CarSystem.SpawnPoints = {
    Civilian = {},
    Police = {},
    Criminal = {},
    Special = {}
}

-- Active spawned cars
CarSystem.SpawnedCars = {}

-- Events
CarSystem.Events = {
    CarSpawned = Instance.new("BindableEvent"),
    CarDestroyed = Instance.new("BindableEvent"),
    CarPurchased = Instance.new("BindableEvent")
}

-- Initialize Car System
function CarSystem:Initialize()
    print("CarSystem: Initializing...")
    
    -- Find spawn points in workspace
    self:FindSpawnPoints()
    
    -- Spawn default cars around the map
    self:SpawnDefaultCars()
    
    print("CarSystem: Initialized successfully!")
end

-- Find spawn points in the workspace
function CarSystem:FindSpawnPoints()
    local spawnPointsFolder = Workspace:FindFirstChild("CarSpawnPoints")
    if not spawnPointsFolder then
        warn("CarSystem: CarSpawnPoints folder not found in Workspace")
        return
    end
    
    for _, spawnPoint in ipairs(spawnPointsFolder:GetChildren()) do
        if spawnPoint:IsA("Part") or spawnPoint:IsA("SpawnLocation") then
            local category = spawnPoint:GetAttribute("Category") or "Civilian"
            
            if not self.SpawnPoints[category] then
                self.SpawnPoints[category] = {}
            end
            
            table.insert(self.SpawnPoints[category], spawnPoint)
        end
    end
    
    print("CarSystem: Found spawn points -", 
          "Civilian:", #self.SpawnPoints.Civilian,
          "Police:", #self.SpawnPoints.Police,
          "Criminal:", #self.SpawnPoints.Criminal,
          "Special:", #self.SpawnPoints.Special)
end

-- Spawn default cars around the map
function CarSystem:SpawnDefaultCars()
    -- Spawn civilian cars
    for i = 1, math.min(5, #self.SpawnPoints.Civilian) do
        local spawnPoint = self.SpawnPoints.Civilian[i]
        self:SpawnCar("BasicCar", spawnPoint.Position, nil, true)
    end
    
    -- Spawn police cars at police station
    for i = 1, math.min(2, #self.SpawnPoints.Police) do
        local spawnPoint = self.SpawnPoints.Police[i]
        self:SpawnCar("PoliceCruiser", spawnPoint.Position, nil, true)
    end
end

-- Spawn a car
function CarSystem:SpawnCar(carType, position, owner, isDefault)
    local config = self.CarConfigs[carType]
    if not config then
        warn("CarSystem: Unknown car type:", carType)
        return nil
    end
    
    -- Find car model in ReplicatedStorage
    local carModelsFolder = ReplicatedStorage:FindFirstChild("CarModels")
    if not carModelsFolder then
        warn("CarSystem: CarModels folder not found in ReplicatedStorage")
        return nil
    end
    
    local carModel = carModelsFolder:FindFirstChild(config.Model)
    if not carModel then
        warn("CarSystem: Car model not found:", config.Model)
        return nil
    end
    
    -- Clone and setup car
    local car = carModel:Clone()
    car.Name = config.Name
    car:SetPrimaryPartCFrame(CFrame.new(position))
    car.Parent = Workspace:FindFirstChild("Cars") or Workspace
    
    -- Add car attributes
    car:SetAttribute("CarType", carType)
    car:SetAttribute("Owner", owner and owner.Name or "None")
    car:SetAttribute("IsDefault", isDefault or false)
    car:SetAttribute("Speed", config.Speed)
    car:SetAttribute("Handling", config.Handling)
    
    -- Setup car physics and controls
    self:SetupCarPhysics(car, config)
    
    -- Add to spawned cars list
    table.insert(self.SpawnedCars, car)
    
    -- Fire event
    self.Events.CarSpawned:Fire(car, owner)
    
    print("CarSystem: Spawned", config.Name, "for", owner and owner.Name or "default")
    return car
end

-- Setup car physics and controls
function CarSystem:SetupCarPhysics(car, config)
    local primaryPart = car.PrimaryPart
    if not primaryPart then return end
    
    -- Add BodyVelocity for movement
    local bodyVelocity = Instance.new("BodyVelocity")
    bodyVelocity.MaxForce = Vector3.new(4000, 0, 4000)
    bodyVelocity.Velocity = Vector3.new(0, 0, 0)
    bodyVelocity.Parent = primaryPart
    
    -- Add BodyAngularVelocity for turning
    local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
    bodyAngularVelocity.MaxTorque = Vector3.new(0, 5000, 0)
    bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
    bodyAngularVelocity.Parent = primaryPart
    
    -- Add car seat for driving
    local seat = car:FindFirstChild("DriveSeat") or car:FindFirstChildOfClass("Seat")
    if seat then
        seat.Name = "DriveSeat"
        
        -- Connect seat events
        seat:GetPropertyChangedSignal("Occupant"):Connect(function()
            if seat.Occupant then
                local player = Players:GetPlayerFromCharacter(seat.Occupant.Parent)
                if player then
                    self:OnPlayerEnteredCar(player, car)
                end
            else
                self:OnPlayerExitedCar(car)
            end
        end)
    end
end

-- Player entered car
function CarSystem:OnPlayerEnteredCar(player, car)
    print("CarSystem:", player.Name, "entered", car.Name)
    
    -- Check if player can use this car
    local carType = car:GetAttribute("CarType")
    local config = self.CarConfigs[carType]
    
    if config.RoleRequired then
        -- Check if player has required role (this would need GameManager integration)
        -- For now, we'll skip this check
    end
    
    if config.GamepassRequired then
        -- Check if player has required gamepass (this would need PlayerData integration)
        -- For now, we'll skip this check
    end
end

-- Player exited car
function CarSystem:OnPlayerExitedCar(car)
    print("CarSystem: Player exited", car.Name)
end

-- Purchase a car
function CarSystem:PurchaseCar(player, carType)
    local config = self.CarConfigs[carType]
    if not config then
        return false, "Invalid car type"
    end
    
    -- This would integrate with PlayerData module
    -- For now, return success
    self.Events.CarPurchased:Fire(player, carType)
    
    print("CarSystem:", player.Name, "purchased", config.Name)
    return true, "Car purchased successfully!"
end

-- Spawn player's owned car
function CarSystem:SpawnPlayerCar(player, carType, spawnLocation)
    -- Check if player owns this car (would integrate with PlayerData)
    
    local spawnPoint
    if spawnLocation then
        spawnPoint = spawnLocation
    else
        -- Find nearest spawn point
        local character = player.Character
        if character and character.PrimaryPart then
            spawnPoint = self:FindNearestSpawnPoint(character.PrimaryPart.Position)
        end
    end
    
    if spawnPoint then
        return self:SpawnCar(carType, spawnPoint, player, false)
    end
    
    return nil
end

-- Find nearest spawn point
function CarSystem:FindNearestSpawnPoint(position)
    local nearestPoint = nil
    local nearestDistance = math.huge
    
    for category, points in pairs(self.SpawnPoints) do
        for _, point in ipairs(points) do
            local distance = (point.Position - position).Magnitude
            if distance < nearestDistance then
                nearestDistance = distance
                nearestPoint = point.Position
            end
        end
    end
    
    return nearestPoint
end

-- Destroy a car
function CarSystem:DestroyCar(car)
    -- Remove from spawned cars list
    for i, spawnedCar in ipairs(self.SpawnedCars) do
        if spawnedCar == car then
            table.remove(self.SpawnedCars, i)
            break
        end
    end
    
    -- Fire event
    self.Events.CarDestroyed:Fire(car)
    
    -- Destroy the car
    car:Destroy()
end

-- Get available cars for player
function CarSystem:GetAvailableCars(player)
    local availableCars = {}
    
    for carType, config in pairs(self.CarConfigs) do
        local canAccess = true
        
        -- Check level requirement (would integrate with PlayerData)
        -- Check gamepass requirement (would integrate with PlayerData)
        -- Check role requirement (would integrate with GameManager)
        
        if canAccess then
            table.insert(availableCars, {
                Type = carType,
                Config = config
            })
        end
    end
    
    return availableCars
end

-- Cleanup old cars
function CarSystem:CleanupOldCars()
    for i = #self.SpawnedCars, 1, -1 do
        local car = self.SpawnedCars[i]
        
        if not car.Parent then
            table.remove(self.SpawnedCars, i)
        elseif car:GetAttribute("IsDefault") then
            -- Check if default car has been abandoned
            local seat = car:FindFirstChild("DriveSeat")
            if seat and not seat.Occupant then
                -- Car has been abandoned, respawn after delay
                -- Implementation would go here
            end
        end
    end
end

return CarSystem
