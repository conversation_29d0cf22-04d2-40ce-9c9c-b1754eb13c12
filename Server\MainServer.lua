-- MainServer.lua - Main server script that initializes all game systems
-- Place this script in ServerScriptService

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")
local RunService = game:GetService("RunService")

-- Create RemoteEvents and RemoteFunctions for client-server communication
local function createRemotes()
    local remotes = Instance.new("Folder")
    remotes.Name = "RemoteEvents"
    remotes.Parent = ReplicatedStorage
    
    -- Game Management
    local gameStateChanged = Instance.new("RemoteEvent")
    gameStateChanged.Name = "GameStateChanged"
    gameStateChanged.Parent = remotes
    
    local roleAssigned = Instance.new("RemoteEvent")
    roleAssigned.Name = "RoleAssigned"
    roleAssigned.Parent = remotes
    
    local roundTimer = Instance.new("RemoteEvent")
    roundTimer.Name = "RoundTimer"
    roundTimer.Parent = remotes
    
    -- Player Data
    local playerDataUpdated = Instance.new("RemoteEvent")
    playerDataUpdated.Name = "PlayerDataUpdated"
    playerDataUpdated.Parent = remotes
    
    local purchaseRequest = Instance.new("RemoteFunction")
    purchaseRequest.Name = "PurchaseRequest"
    purchaseRequest.Parent = remotes
    
    -- Car System
    local spawnCarRequest = Instance.new("RemoteFunction")
    spawnCarRequest.Name = "SpawnCarRequest"
    spawnCarRequest.Parent = remotes
    
    local carSpawned = Instance.new("RemoteEvent")
    carSpawned.Name = "CarSpawned"
    carSpawned.Parent = remotes
    
    -- Crime System
    local crimeAssigned = Instance.new("RemoteEvent")
    crimeAssigned.Name = "CrimeAssigned"
    crimeAssigned.Parent = remotes
    
    local crimeProgress = Instance.new("RemoteFunction")
    crimeProgress.Name = "CrimeProgress"
    crimeProgress.Parent = remotes
    
    local arrestRequest = Instance.new("RemoteFunction")
    arrestRequest.Name = "ArrestRequest"
    arrestRequest.Parent = remotes
    
    -- Sound System
    local soundToggle = Instance.new("RemoteFunction")
    soundToggle.Name = "SoundToggle"
    soundToggle.Parent = remotes
    
    print("MainServer: Created remote events and functions")
end

-- Load modules
local function loadModules()
    -- Get modules from ModulesScript folder
    local modulesFolder = script.Parent.Parent:FindFirstChild("ModulesScript")
    if not modulesFolder then
        error("MainServer: ModulesScript folder not found!")
    end
    
    local GameManager = require(modulesFolder:FindFirstChild("GameManager"))
    local PlayerData = require(modulesFolder:FindFirstChild("PlayerData"))
    local CarSystem = require(modulesFolder:FindFirstChild("CarSystem"))
    local CrimeSystem = require(modulesFolder:FindFirstChild("CrimeSystem"))
    
    return GameManager, PlayerData, CarSystem, CrimeSystem
end

-- Main initialization
local function initialize()
    print("MainServer: Starting game server...")
    
    -- Create remote events first
    createRemotes()
    
    -- Load all modules
    local GameManager, PlayerData, CarSystem, CrimeSystem = loadModules()
    
    -- Initialize all systems
    PlayerData:Initialize()
    CarSystem:Initialize()
    CrimeSystem:Initialize()
    GameManager:Initialize()
    
    -- Get remote events
    local remotes = ReplicatedStorage:WaitForChild("RemoteEvents")
    local gameStateChanged = remotes:WaitForChild("GameStateChanged")
    local roleAssigned = remotes:WaitForChild("RoleAssigned")
    local roundTimer = remotes:WaitForChild("RoundTimer")
    local playerDataUpdated = remotes:WaitForChild("PlayerDataUpdated")
    local purchaseRequest = remotes:WaitForChild("PurchaseRequest")
    local spawnCarRequest = remotes:WaitForChild("SpawnCarRequest")
    local carSpawned = remotes:WaitForChild("CarSpawned")
    local crimeAssigned = remotes:WaitForChild("CrimeAssigned")
    local crimeProgress = remotes:WaitForChild("CrimeProgress")
    local arrestRequest = remotes:WaitForChild("ArrestRequest")
    local soundToggle = remotes:WaitForChild("SoundToggle")
    
    -- Connect GameManager events
    GameManager.Events.StateChanged.Event:Connect(function(oldState, newState)
        gameStateChanged:FireAllClients(oldState, newState)
    end)
    
    GameManager.Events.PlayerRoleAssigned.Event:Connect(function(player, role)
        roleAssigned:FireClient(player, role)
        
        -- If player is criminal, assign a crime
        if role == "Criminal" then
            local crime = CrimeSystem:AssignRandomCrime(player)
            if crime then
                crimeAssigned:FireClient(player, crime)
            end
        end
    end)
    
    -- Connect PlayerData events
    PlayerData.Events.DataUpdated.Event:Connect(function(player, key, value)
        playerDataUpdated:FireClient(player, key, value)
    end)
    
    PlayerData.Events.CashChanged.Event:Connect(function(player, oldCash, newCash)
        playerDataUpdated:FireClient(player, "Cash", newCash)
    end)
    
    PlayerData.Events.LevelUp.Event:Connect(function(player, oldLevel, newLevel)
        playerDataUpdated:FireClient(player, "Level", newLevel)
        -- Award level up bonus
        PlayerData:AddCash(player, newLevel * 100)
    end)
    
    -- Connect CarSystem events
    CarSystem.Events.CarSpawned.Event:Connect(function(car, owner)
        if owner then
            carSpawned:FireClient(owner, car.Name, car:GetAttribute("CarType"))
        end
    end)
    
    -- Connect CrimeSystem events
    CrimeSystem.Events.CrimeCompleted.Event:Connect(function(player, crime, cashReward, xpReward)
        -- Award rewards
        PlayerData:AddCash(player, cashReward)
        PlayerData:AddXP(player, xpReward)
        PlayerData:IncrementStat(player, "CrimesCommitted")
        
        print("MainServer:", player.Name, "completed crime and earned", cashReward, "cash")
    end)
    
    CrimeSystem.Events.CrimeFailed.Event:Connect(function(player, crime, reason)
        if reason:find("Arrested") then
            PlayerData:IncrementStat(player, "TimesArrested")
        end
        
        print("MainServer:", player.Name, "failed crime -", reason)
    end)
    
    -- Handle remote function calls
    purchaseRequest.OnServerInvoke = function(player, itemType, itemName)
        if itemType == "Car" then
            local config = CarSystem.CarConfigs[itemName]
            if config then
                local playerCash = PlayerData:GetCash(player)
                local playerLevel = PlayerData:GetLevel(player)
                
                -- Check requirements
                if playerCash >= config.Price and playerLevel >= config.RequiredLevel then
                    -- Check gamepass requirement
                    if config.GamepassRequired and not PlayerData:HasGamepass(player, config.GamepassRequired) then
                        return false, "Gamepass required: " .. config.GamepassRequired
                    end
                    
                    -- Check if already owned
                    if PlayerData:HasCar(player, itemName) then
                        return false, "You already own this car"
                    end
                    
                    -- Purchase car
                    if PlayerData:RemoveCash(player, config.Price) then
                        PlayerData:AddCar(player, itemName)
                        return true, "Car purchased successfully!"
                    else
                        return false, "Transaction failed"
                    end
                else
                    return false, "Insufficient cash or level"
                end
            end
        end
        
        return false, "Invalid purchase request"
    end
    
    spawnCarRequest.OnServerInvoke = function(player, carType)
        if PlayerData:HasCar(player, carType) then
            local car = CarSystem:SpawnPlayerCar(player, carType)
            if car then
                return true, "Car spawned successfully!"
            else
                return false, "Failed to spawn car"
            end
        else
            return false, "You don't own this car"
        end
    end
    
    crimeProgress.OnServerInvoke = function(player, stepNumber)
        return CrimeSystem:UpdateCrimeProgress(player, stepNumber)
    end
    
    arrestRequest.OnServerInvoke = function(policePlayer, criminalPlayer)
        local policeRole = GameManager:GetPlayerRole(policePlayer)
        local criminalRole = GameManager:GetPlayerRole(criminalPlayer)
        
        if policeRole == "Police" and criminalRole == "Criminal" then
            -- Check if players are close enough
            if policePlayer.Character and criminalPlayer.Character then
                local distance = (policePlayer.Character.PrimaryPart.Position - 
                                criminalPlayer.Character.PrimaryPart.Position).Magnitude
                
                if distance <= 10 then -- 10 studs arrest range
                    local success = CrimeSystem:ArrestCriminal(criminalPlayer, policePlayer)
                    if success then
                        -- Reward police officer
                        PlayerData:AddCash(policePlayer, 500)
                        PlayerData:AddXP(policePlayer, 25)
                        PlayerData:IncrementStat(policePlayer, "CriminalsArrested")
                        
                        return true, "Criminal arrested!"
                    end
                end
            end
        end
        
        return false, "Arrest failed"
    end
    
    soundToggle.OnServerInvoke = function(player, soundType, enabled)
        -- Update player settings
        local settings = PlayerData:GetData(player, "Settings") or {}
        if soundType == "Music" then
            settings.SoundEnabled = enabled
        elseif soundType == "SFX" then
            settings.SFXVolume = enabled and 0.7 or 0
        end
        PlayerData:SetData(player, "Settings", settings)
        
        return true
    end
    
    -- Timer update loop
    spawn(function()
        while true do
            wait(1)
            
            -- Update round timer for all clients
            local timeRemaining = GameManager:GetTimeRemaining()
            roundTimer:FireAllClients(timeRemaining, GameManager.CurrentState)
            
            -- Check crime timeouts
            CrimeSystem:CheckCrimeTimeouts()
            
            -- Cleanup old data
            if tick() % 300 == 0 then -- Every 5 minutes
                CrimeSystem:CleanupOldCrimes()
                CarSystem:CleanupOldCars()
            end
        end
    end)
    
    print("MainServer: Game server initialized successfully!")
end

-- Start the server
initialize()
