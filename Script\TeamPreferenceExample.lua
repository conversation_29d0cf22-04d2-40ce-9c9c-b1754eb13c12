-- TeamPreferenceExample.lua - Example implementation for team preferences
-- This shows how to properly implement the team preference system

-- SERVER SIDE (Add to MainServer.lua)
--[[
-- Add this to your remote events creation in MainServer.lua:
local setTeamPreference = Instance.new("RemoteFunction")
setTeamPreference.Name = "SetTeamPreference"
setTeamPreference.Parent = remotes

-- Add this to your remote function handlers:
setTeamPreference.OnServerInvoke = function(player, preferredTeam)
    -- Validate the team preference
    local validTeams = {"Any", "Criminal", "Police", "Citizen"}
    
    if not table.find(validTeams, preferredTeam) then
        return false, "Invalid team preference"
    end
    
    -- Save to player data
    local settings = PlayerData:GetData(player, "Settings") or {}
    settings.PreferredRole = preferredTeam
    PlayerData:SetData(player, "Settings", settings)
    
    print("Server: Set team preference for", player.Name, "to", preferredTeam)
    return true, "Team preference saved!"
end

-- Modify the role assignment in GameManager.lua to consider preferences:
function GameManager:AssignRolesWithPreferences()
    local availablePlayers = {}
    local criminalPrefs = {}
    local policePrefs = {}
    local citizenPrefs = {}
    
    -- Sort players by preferences
    for _, player in ipairs(self.PlayersInGame) do
        local settings = PlayerData:GetData(player, "Settings") or {}
        local preference = settings.PreferredRole or "Any"
        
        table.insert(availablePlayers, player)
        
        if preference == "Criminal" then
            table.insert(criminalPrefs, player)
        elseif preference == "Police" then
            table.insert(policePrefs, player)
        elseif preference == "Citizen" then
            table.insert(citizenPrefs, player)
        end
    end
    
    -- Assign criminal (prefer players who want to be criminal)
    local criminal
    if #criminalPrefs > 0 then
        criminal = criminalPrefs[math.random(1, #criminalPrefs)]
    else
        criminal = availablePlayers[math.random(1, #availablePlayers)]
    end
    
    self.CurrentRoles[criminal] = "Criminal"
    
    -- Remove criminal from available players
    for i, player in ipairs(availablePlayers) do
        if player == criminal then
            table.remove(availablePlayers, i)
            break
        end
    end
    
    -- Assign police (prefer players who want to be police)
    local policeCount = math.min(self.Settings.POLICE_COUNT, #availablePlayers)
    local assignedPolice = 0
    
    -- First, assign from police preferences
    for _, player in ipairs(policePrefs) do
        if assignedPolice < policeCount and table.find(availablePlayers, player) then
            self.CurrentRoles[player] = "Police"
            assignedPolice = assignedPolice + 1
            
            -- Remove from available
            for i, p in ipairs(availablePlayers) do
                if p == player then
                    table.remove(availablePlayers, i)
                    break
                end
            end
        end
    end
    
    -- Fill remaining police slots randomly
    while assignedPolice < policeCount and #availablePlayers > 0 do
        local randomIndex = math.random(1, #availablePlayers)
        local player = availablePlayers[randomIndex]
        
        self.CurrentRoles[player] = "Police"
        assignedPolice = assignedPolice + 1
        table.remove(availablePlayers, randomIndex)
    end
    
    -- Remaining players are citizens
    for _, player in ipairs(availablePlayers) do
        self.CurrentRoles[player] = "Citizen"
    end
    
    -- Fire role assignment events
    for player, role in pairs(self.CurrentRoles) do
        self.Events.PlayerRoleAssigned:Fire(player, role)
        print("GameManager: Assigned", role, "to", player.Name)
    end
end
--]]

-- CLIENT SIDE (Add to StartGui/MainGui.lua)
--[[
-- Add this at the top with other remote events:
local setTeamPreference = remotes:WaitForChild("SetTeamPreference")

-- Replace the team button click handler with this:
teamButton.MouseButton1Click:Connect(function()
    print("Team preference set to:", team.name)
    
    -- Send preference to server
    local success, message = setTeamPreference:InvokeServer(team.name)
    
    if success then
        -- Update visual feedback - highlight selected button
        for _, otherButton in pairs(teamSection:GetChildren()) do
            if otherButton:IsA("TextButton") and otherButton.Name:find("Button") then
                otherButton.BackgroundTransparency = 0
                otherButton.TextColor3 = Color3.fromRGB(255, 255, 255)
            end
        end
        
        -- Highlight selected button
        teamButton.BackgroundTransparency = 0.3
        teamButton.TextColor3 = Color3.fromRGB(255, 255, 100) -- Yellow text for selected
        
        -- Show confirmation message
        local confirmText = Instance.new("TextLabel")
        confirmText.Size = UDim2.new(1, -20, 0, 20)
        confirmText.Position = UDim2.new(0, 10, 1, -25)
        confirmText.BackgroundTransparency = 1
        confirmText.Text = "✓ " .. message
        confirmText.TextColor3 = Color3.fromRGB(0, 255, 0)
        confirmText.TextScaled = true
        confirmText.Font = Enum.Font.Gotham
        confirmText.Parent = teamSection
        
        -- Remove confirmation after 3 seconds
        game:GetService("Debris"):AddItem(confirmText, 3)
        
    else
        warn("Failed to set team preference:", message)
    end
end)
--]]

-- ENHANCED TEAM SELECTION WITH ICONS AND DESCRIPTIONS
--[[
-- Replace the teams table in createTeamMenu() with this enhanced version:
local teams = {
    {
        name = "Any", 
        color = Color3.fromRGB(100, 100, 100), 
        icon = "🎲",
        description = "Let the game decide your role randomly"
    },
    {
        name = "Criminal", 
        color = Color3.fromRGB(200, 50, 50), 
        icon = "🔫",
        description = "Prefer to be the criminal and commit crimes"
    },
    {
        name = "Police", 
        color = Color3.fromRGB(50, 50, 200), 
        icon = "👮",
        description = "Prefer to be police and catch criminals"
    },
    {
        name = "Citizen", 
        color = Color3.fromRGB(50, 200, 50), 
        icon = "👤",
        description = "Prefer to be a citizen and help or hide"
    }
}

-- Enhanced team button creation:
for i, team in ipairs(teams) do
    local teamButton = Instance.new("TextButton")
    teamButton.Name = team.name .. "Button"
    teamButton.Size = UDim2.new(0.22, 0, 0, 80) -- Taller for description
    teamButton.Position = UDim2.new((i-1) * 0.25 + 0.02, 0, 0, 50)
    teamButton.BackgroundColor3 = team.color
    teamButton.BorderSizePixel = 0
    teamButton.Parent = teamSection
    
    local buttonCorner = Instance.new("UICorner")
    buttonCorner.CornerRadius = UDim.new(0, 8)
    buttonCorner.Parent = teamButton
    
    -- Icon and name
    local iconLabel = Instance.new("TextLabel")
    iconLabel.Size = UDim2.new(1, 0, 0, 25)
    iconLabel.Position = UDim2.new(0, 0, 0, 5)
    iconLabel.BackgroundTransparency = 1
    iconLabel.Text = team.icon .. " " .. team.name
    iconLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    iconLabel.TextScaled = true
    iconLabel.Font = Enum.Font.GothamBold
    iconLabel.Parent = teamButton
    
    -- Description
    local descLabel = Instance.new("TextLabel")
    descLabel.Size = UDim2.new(1, -10, 0, 45)
    descLabel.Position = UDim2.new(0, 5, 0, 30)
    descLabel.BackgroundTransparency = 1
    descLabel.Text = team.description
    descLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    descLabel.TextScaled = true
    descLabel.Font = Enum.Font.Gotham
    descLabel.TextWrapped = true
    descLabel.Parent = teamButton
    
    -- Click handler (same as above)
    teamButton.MouseButton1Click:Connect(function()
        -- Implementation here
    end)
end
--]]

print("TeamPreferenceExample: This file contains example code for implementing team preferences")
print("Copy the relevant sections to your actual game scripts as needed")
