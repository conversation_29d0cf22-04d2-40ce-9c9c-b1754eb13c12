# 🎮 Crime Game - Script Documentation

## 📋 Script Overview

This folder contains additional utility scripts and examples for your crime game.

### 🔧 Utility Scripts

#### CarController.lua (Example)
```lua
-- CarController.lua - Enhanced car physics and controls
-- Place in ServerScriptService for server-side car management

local CarController = {}

-- Enhanced car physics
function CarController:SetupAdvancedPhysics(car)
    local primaryPart = car.PrimaryPart
    if not primaryPart then return end
    
    -- Add realistic car physics
    local bodyVelocity = Instance.new("BodyVelocity")
    bodyVelocity.MaxForce = Vector3.new(5000, 0, 5000)
    bodyVelocity.Velocity = Vector3.new(0, 0, 0)
    bodyVelocity.Parent = primaryPart
    
    local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
    bodyAngularVelocity.MaxTorque = Vector3.new(0, 8000, 0)
    bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
    bodyAngularVelocity.Parent = primaryPart
    
    -- Add suspension (optional)
    for _, wheel in pairs(car:GetChildren()) do
        if wheel.Name:find("Wheel") and wheel:IsA("Part") then
            local spring = Instance.new("SpringConstraint")
            spring.Attachment0 = wheel:FindFirstChild("Attachment")
            spring.Attachment1 = primaryPart:FindFirstChild("Attachment")
            spring.Stiffness = 20000
            spring.Damping = 500
            spring.Parent = wheel
        end
    end
end

return CarController
```

#### SoundManager.lua (Example)
```lua
-- SoundManager.lua - Centralized sound management
-- Place in ReplicatedStorage/ModulesScript

local SoundManager = {}
local SoundService = game:GetService("SoundService")

-- Sound configurations
SoundManager.Sounds = {
    BackgroundMusic = {
        SoundId = "rbxassetid://1234567890", -- Replace with your music ID
        Volume = 0.5,
        Looped = true
    },
    
    CarEngine = {
        SoundId = "rbxassetid://1234567891",
        Volume = 0.7,
        Looped = true
    },
    
    Arrest = {
        SoundId = "rbxassetid://1234567892",
        Volume = 0.8,
        Looped = false
    },
    
    CrimeComplete = {
        SoundId = "rbxassetid://1234567893",
        Volume = 0.6,
        Looped = false
    }
}

-- Play a sound
function SoundManager:PlaySound(soundName, parent)
    local config = self.Sounds[soundName]
    if not config then return end
    
    local sound = Instance.new("Sound")
    sound.SoundId = config.SoundId
    sound.Volume = config.Volume
    sound.Looped = config.Looped
    sound.Parent = parent or SoundService
    
    sound:Play()
    
    if not config.Looped then
        sound.Ended:Connect(function()
            sound:Destroy()
        end)
    end
    
    return sound
end

return SoundManager
```

### 🎯 Crime Mission Scripts

#### BankRobbery.lua (Example)
```lua
-- BankRobbery.lua - Specific bank robbery mechanics
-- Place in ServerScriptService

local BankRobbery = {}
local TweenService = game:GetService("TweenService")

function BankRobbery:StartRobbery(player, bankLocation)
    print("Bank robbery started by", player.Name)
    
    -- Find vault door
    local vault = bankLocation:FindFirstChild("Vault")
    if not vault then return false end
    
    -- Animate vault opening
    local door = vault:FindFirstChild("Door")
    if door then
        local openTween = TweenService:Create(door,
            TweenInfo.new(3, Enum.EasingStyle.Quad),
            {CFrame = door.CFrame * CFrame.Angles(0, math.rad(90), 0)}
        )
        openTween:Play()
    end
    
    -- Spawn money bags
    for i = 1, 5 do
        local moneyBag = Instance.new("Part")
        moneyBag.Name = "MoneyBag"
        moneyBag.Size = Vector3.new(2, 2, 2)
        moneyBag.Material = Enum.Material.Neon
        moneyBag.BrickColor = BrickColor.new("Bright green")
        moneyBag.Position = vault.Position + Vector3.new(math.random(-5, 5), 2, math.random(-5, 5))
        moneyBag.Parent = workspace
        
        -- Add collection detection
        local detector = Instance.new("Part")
        detector.Size = Vector3.new(4, 4, 4)
        detector.Transparency = 1
        detector.CanCollide = false
        detector.Position = moneyBag.Position
        detector.Parent = moneyBag
        
        detector.Touched:Connect(function(hit)
            local character = hit.Parent
            if character:FindFirstChild("Humanoid") and Players:GetPlayerFromCharacter(character) == player then
                moneyBag:Destroy()
                -- Award money
                print(player.Name, "collected money bag!")
            end
        end)
    end
    
    return true
end

return BankRobbery
```

### 🚔 Police Tools

#### ArrestTool.lua (Example)
```lua
-- ArrestTool.lua - Police arrest tool
-- Place this script inside a Tool in StarterPack (for police only)

local tool = script.Parent
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Remote events
local remotes = ReplicatedStorage:WaitForChild("RemoteEvents")
local arrestRequest = remotes:WaitForChild("ArrestRequest")

tool.Activated:Connect(function()
    local target = mouse.Target
    if not target then return end
    
    local character = target.Parent
    local targetPlayer = Players:GetPlayerFromCharacter(character)
    
    if targetPlayer and targetPlayer ~= player then
        -- Check distance
        local distance = (player.Character.PrimaryPart.Position - 
                         targetPlayer.Character.PrimaryPart.Position).Magnitude
        
        if distance <= 15 then
            local success, message = arrestRequest:InvokeServer(targetPlayer)
            
            if success then
                -- Play arrest animation
                local humanoid = player.Character:FindFirstChild("Humanoid")
                if humanoid then
                    humanoid:LoadAnimation(script:FindFirstChild("ArrestAnimation")):Play()
                end
            end
        end
    end
end)
```

### 🎨 GUI Enhancements

#### NotificationSystem.lua (Example)
```lua
-- NotificationSystem.lua - In-game notification system
-- Place in StarterPlayerScripts

local NotificationSystem = {}
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Create notification GUI
local function createNotificationGui()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "NotificationGui"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    return screenGui
end

-- Show notification
function NotificationSystem:ShowNotification(title, message, duration, color)
    local gui = playerGui:FindFirstChild("NotificationGui") or createNotificationGui()
    
    local notification = Instance.new("Frame")
    notification.Size = UDim2.new(0, 300, 0, 80)
    notification.Position = UDim2.new(1, 10, 0, 100)
    notification.BackgroundColor3 = color or Color3.fromRGB(50, 50, 50)
    notification.BorderSizePixel = 0
    notification.Parent = gui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = notification
    
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, -20, 0, 25)
    titleLabel.Position = UDim2.new(0, 10, 0, 5)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = title
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.GothamBold
    titleLabel.Parent = notification
    
    local messageLabel = Instance.new("TextLabel")
    messageLabel.Size = UDim2.new(1, -20, 0, 40)
    messageLabel.Position = UDim2.new(0, 10, 0, 30)
    messageLabel.BackgroundTransparency = 1
    messageLabel.Text = message
    messageLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    messageLabel.TextScaled = true
    messageLabel.Font = Enum.Font.Gotham
    messageLabel.TextWrapped = true
    messageLabel.Parent = notification
    
    -- Animate in
    local slideIn = TweenService:Create(notification,
        TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
        {Position = UDim2.new(1, -310, 0, 100)}
    )
    slideIn:Play()
    
    -- Auto-remove after duration
    game:GetService("Debris"):AddItem(notification, duration or 5)
    
    -- Animate out before removal
    spawn(function()
        wait((duration or 5) - 0.5)
        local slideOut = TweenService:Create(notification,
            TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
            {Position = UDim2.new(1, 10, 0, 100)}
        )
        slideOut:Play()
    end)
end

return NotificationSystem
```

### 📊 Analytics Scripts

#### GameAnalytics.lua (Example)
```lua
-- GameAnalytics.lua - Track game statistics
-- Place in ServerScriptService

local GameAnalytics = {}
local HttpService = game:GetService("HttpService")

-- Game statistics
GameAnalytics.Stats = {
    TotalGames = 0,
    TotalCrimes = 0,
    TotalArrests = 0,
    AverageGameTime = 0,
    PopularCrimes = {},
    PlayerRetention = {}
}

-- Track game event
function GameAnalytics:TrackEvent(eventType, data)
    print("Analytics:", eventType, HttpService:JSONEncode(data))
    
    -- Here you could send data to external analytics service
    -- or save to DataStore for later analysis
end

-- Track player session
function GameAnalytics:TrackPlayerSession(player, action)
    local sessionData = {
        PlayerId = player.UserId,
        PlayerName = player.Name,
        Action = action,
        Timestamp = os.time(),
        GameState = "Playing" -- Get from GameManager
    }
    
    self:TrackEvent("PlayerSession", sessionData)
end

return GameAnalytics
```

## 🚀 Usage Examples

### Adding Custom Crime Types:
1. Edit `ModulesScript/CrimeSystem.lua`
2. Add new entry to `CrimeTypes` table
3. Create corresponding location in workspace
4. Test the new crime mission

### Creating New Car Models:
1. Build car model in workspace
2. Set PrimaryPart to main body
3. Add DriveSeat (Seat or VehicleSeat)
4. Move to ReplicatedStorage/CarModels
5. Add configuration to CarSystem.lua

### Custom GUI Themes:
1. Modify Color3.fromRGB values in GUI scripts
2. Change corner radius and stroke thickness
3. Add new icons and images
4. Customize fonts and text sizes

## 📝 Notes

- All example scripts are optional enhancements
- Test thoroughly before adding to your game
- Modify configurations to match your game's theme
- Consider performance impact of additional features

## 🔗 Integration

These scripts integrate with the main game systems:
- Use existing RemoteEvents for communication
- Follow the same coding patterns and structure
- Maintain compatibility with core modules
- Extend functionality without breaking existing features
