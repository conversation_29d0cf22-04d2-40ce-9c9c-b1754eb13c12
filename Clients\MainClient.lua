-- MainClient.lua - Main client script for GUI and game management
-- Place this script in StarterPlayer > StarterPlayerScripts

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local SoundService = game:GetService("SoundService")
local StarterGui = game:GetService("StarterGui")

-- Player and GUI references
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Game state variables
local currentRole = "Spectator"
local currentGameState = "Waiting"
local playerData = {}
local currentCrime = nil

-- GUI elements (will be created)
local mainGui = nil
local roleGui = nil
local crimeGui = nil
local carShopGui = nil
local soundButtons = nil

-- Remote events
local remotes = ReplicatedStorage:WaitForChild("RemoteEvents")
local gameStateChanged = remotes:WaitForChild("GameStateChanged")
local roleAssigned = remotes:WaitForChild("RoleAssigned")
local roundTimer = remotes:WaitForChild("RoundTimer")
local playerDataUpdated = remotes:WaitForChild("PlayerDataUpdated")
local purchaseRequest = remotes:WaitForChild("PurchaseRequest")
local spawnCarRequest = remotes:WaitForChild("SpawnCarRequest")
local carSpawned = remotes:WaitForChild("CarSpawned")
local crimeAssigned = remotes:WaitForChild("CrimeAssigned")
local crimeProgress = remotes:WaitForChild("CrimeProgress")
local arrestRequest = remotes:WaitForChild("ArrestRequest")
local soundToggle = remotes:WaitForChild("SoundToggle")

-- Create main GUI structure
local function createMainGui()
	-- Main GUI container
	mainGui = Instance.new("ScreenGui")
	mainGui.Name = "MainGui"
	mainGui.ResetOnSpawn = false
	mainGui.Parent = playerGui

	-- Level Frame
	local levelFrame = Instance.new("Frame")
	levelFrame.Name = "LevelFrame"
	levelFrame.Size = UDim2.new(0, 300, 0, 80)
	levelFrame.Position = UDim2.new(0, 10, 0, 10)
	levelFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	levelFrame.BorderSizePixel = 0
	levelFrame.Parent = mainGui

	-- Level frame corner
	local levelCorner = Instance.new("UICorner")
	levelCorner.CornerRadius = UDim.new(0, 8)
	levelCorner.Parent = levelFrame

	-- XP Bar Background
	local xpBarBG = Instance.new("Frame")
	xpBarBG.Name = "BarXpLevelFRAME"
	xpBarBG.Size = UDim2.new(1, -20, 0, 20)
	xpBarBG.Position = UDim2.new(0, 10, 0, 45)
	xpBarBG.BackgroundColor3 = Color3.fromRGB(20, 20, 20)
	xpBarBG.BorderSizePixel = 0
	xpBarBG.Parent = levelFrame

	local xpBarCorner = Instance.new("UICorner")
	xpBarCorner.CornerRadius = UDim.new(0, 4)
	xpBarCorner.Parent = xpBarBG

	-- XP Bar Fill
	local xpBarFill = Instance.new("Frame")
	xpBarFill.Name = "XPFill"
	xpBarFill.Size = UDim2.new(0.5, 0, 1, 0)
	xpBarFill.Position = UDim2.new(0, 0, 0, 0)
	xpBarFill.BackgroundColor3 = Color3.fromRGB(0, 255, 100)
	xpBarFill.BorderSizePixel = 0
	xpBarFill.Parent = xpBarBG

	local xpFillCorner = Instance.new("UICorner")
	xpFillCorner.CornerRadius = UDim.new(0, 4)
	xpFillCorner.Parent = xpBarFill

	-- Level and XP Text
	local levelText = Instance.new("TextLabel")
	levelText.Name = "Xp_LevelText"
	levelText.Size = UDim2.new(1, -20, 0, 35)
	levelText.Position = UDim2.new(0, 10, 0, 5)
	levelText.BackgroundTransparency = 1
	levelText.Text = "Level 1 - 0/100 XP"
	levelText.TextColor3 = Color3.fromRGB(255, 255, 255)
	levelText.TextScaled = true
	levelText.Font = Enum.Font.GothamBold
	levelText.Parent = levelFrame

	-- Menu Button
	local menuButton = Instance.new("TextButton")
	menuButton.Name = "MenuButton"
	menuButton.Size = UDim2.new(0, 100, 0, 40)
	menuButton.Position = UDim2.new(1, -110, 0, 10)
	menuButton.BackgroundColor3 = Color3.fromRGB(0, 120, 255)
	menuButton.Text = "MENU"
	menuButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	menuButton.TextScaled = true
	menuButton.Font = Enum.Font.GothamBold
	menuButton.BorderSizePixel = 0
	menuButton.Parent = mainGui

	local menuCorner = Instance.new("UICorner")
	menuCorner.CornerRadius = UDim.new(0, 8)
	menuCorner.Parent = menuButton

	-- Sound Buttons Frame
	local soundFrame = Instance.new("Frame")
	soundFrame.Name = "SoundButtons"
	soundFrame.Size = UDim2.new(0, 120, 0, 40)
	soundFrame.Position = UDim2.new(1, -240, 0, 10)
	soundFrame.BackgroundTransparency = 1
	soundFrame.Parent = mainGui

	-- Sound ON Button
	local soundOnButton = Instance.new("ImageButton")
	soundOnButton.Name = "SoundOn"
	soundOnButton.Size = UDim2.new(0, 35, 0, 35)
	soundOnButton.Position = UDim2.new(0, 0, 0, 0)
	soundOnButton.BackgroundColor3 = Color3.fromRGB(0, 255, 0)
	soundOnButton.Image = "rbxasset://textures/ui/VoiceChat/SpeakerLight.png"
	soundOnButton.Parent = soundFrame

	local soundOnCorner = Instance.new("UICorner")
	soundOnCorner.CornerRadius = UDim.new(0, 6)
	soundOnCorner.Parent = soundOnButton

	-- Sound OFF Button
	local soundOffButton = Instance.new("ImageButton")
	soundOffButton.Name = "SoundOff"
	soundOffButton.Size = UDim2.new(0, 35, 0, 35)
	soundOffButton.Position = UDim2.new(0, 45, 0, 0)
	soundOffButton.BackgroundColor3 = Color3.fromRGB(255, 0, 0)
	soundOffButton.Image = "rbxasset://textures/ui/VoiceChat/MicLight.png"
	soundOffButton.Parent = soundFrame

	local soundOffCorner = Instance.new("UICorner")
	soundOffCorner.CornerRadius = UDim.new(0, 6)
	soundOffCorner.Parent = soundOffButton

	-- Timer Display
	local timerFrame = Instance.new("Frame")
	timerFrame.Name = "TimerFrame"
	timerFrame.Size = UDim2.new(0, 200, 0, 60)
	timerFrame.Position = UDim2.new(0.5, -100, 0, 10)
	timerFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	timerFrame.BorderSizePixel = 0
	timerFrame.Parent = mainGui

	local timerCorner = Instance.new("UICorner")
	timerCorner.CornerRadius = UDim.new(0, 8)
	timerCorner.Parent = timerFrame

	local timerText = Instance.new("TextLabel")
	timerText.Name = "TimerText"
	timerText.Size = UDim2.new(1, -20, 1, -20)
	timerText.Position = UDim2.new(0, 10, 0, 10)
	timerText.BackgroundTransparency = 1
	timerText.Text = "Waiting for players..."
	timerText.TextColor3 = Color3.fromRGB(255, 255, 255)
	timerText.TextScaled = true
	timerText.Font = Enum.Font.GothamBold
	timerText.Parent = timerFrame

	-- Connect menu button
	menuButton.MouseButton1Click:Connect(function()
		toggleMenu()
	end)

	-- Connect sound buttons
	soundOnButton.MouseButton1Click:Connect(function()
		toggleSound(true)
	end)

	soundOffButton.MouseButton1Click:Connect(function()
		toggleSound(false)
	end)

	print("MainClient: Created main GUI")
end

-- Create role-specific GUI
local function createRoleGui()
	roleGui = Instance.new("ScreenGui")
	roleGui.Name = "RoleGui"
	roleGui.ResetOnSpawn = false
	roleGui.Parent = playerGui

	-- Role indicator
	local roleFrame = Instance.new("Frame")
	roleFrame.Name = "RoleFrame"
	roleFrame.Size = UDim2.new(0, 250, 0, 100)
	roleFrame.Position = UDim2.new(0, 10, 0, 100)
	roleFrame.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
	roleFrame.BorderSizePixel = 0
	roleFrame.Parent = roleGui

	local roleCorner = Instance.new("UICorner")
	roleCorner.CornerRadius = UDim.new(0, 8)
	roleCorner.Parent = roleFrame

	local roleTitle = Instance.new("TextLabel")
	roleTitle.Name = "RoleTitle"
	roleTitle.Size = UDim2.new(1, -20, 0, 30)
	roleTitle.Position = UDim2.new(0, 10, 0, 5)
	roleTitle.BackgroundTransparency = 1
	roleTitle.Text = "YOUR ROLE"
	roleTitle.TextColor3 = Color3.fromRGB(200, 200, 200)
	roleTitle.TextScaled = true
	roleTitle.Font = Enum.Font.Gotham
	roleTitle.Parent = roleFrame

	local roleText = Instance.new("TextLabel")
	roleText.Name = "RoleText"
	roleText.Size = UDim2.new(1, -20, 0, 40)
	roleText.Position = UDim2.new(0, 10, 0, 35)
	roleText.BackgroundTransparency = 1
	roleText.Text = "SPECTATOR"
	roleText.TextColor3 = Color3.fromRGB(255, 255, 255)
	roleText.TextScaled = true
	roleText.Font = Enum.Font.GothamBold
	roleText.Parent = roleFrame

	local roleDescription = Instance.new("TextLabel")
	roleDescription.Name = "RoleDescription"
	roleDescription.Size = UDim2.new(1, -20, 0, 20)
	roleDescription.Position = UDim2.new(0, 10, 0, 75)
	roleDescription.BackgroundTransparency = 1
	roleDescription.Text = "Wait for the next round to start"
	roleDescription.TextColor3 = Color3.fromRGB(180, 180, 180)
	roleDescription.TextScaled = true
	roleDescription.Font = Enum.Font.Gotham
	roleDescription.Parent = roleFrame

	print("MainClient: Created role GUI")
end

-- Update player data display
local function updatePlayerDataDisplay()
	if not mainGui then return end

	local levelText = mainGui:FindFirstChild("LevelFrame"):FindFirstChild("Xp_LevelText")
	local xpFill = mainGui:FindFirstChild("LevelFrame"):FindFirstChild("BarXpLevelFRAME"):FindFirstChild("XPFill")

	if levelText and playerData.Level and playerData.XP and playerData.XPToNextLevel then
		levelText.Text = string.format("Level %d - %d/%d XP", 
			playerData.Level, 
			playerData.XP, 
			playerData.XPToNextLevel)

		if xpFill then
			local xpPercent = playerData.XP / playerData.XPToNextLevel
			xpFill.Size = UDim2.new(xpPercent, 0, 1, 0)
		end
	end
end

-- Update role display
local function updateRoleDisplay(role)
	if not roleGui then return end

	local roleText = roleGui:FindFirstChild("RoleFrame"):FindFirstChild("RoleText")
	local roleDescription = roleGui:FindFirstChild("RoleFrame"):FindFirstChild("RoleDescription")
	local roleFrame = roleGui:FindFirstChild("RoleFrame")

	if roleText then
		roleText.Text = role:upper()
		currentRole = role

		-- Update colors and descriptions based on role
		if role == "Criminal" then
			roleFrame.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
			roleDescription.Text = "Complete your crime mission and escape!"
		elseif role == "Police" then
			roleFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 200)
			roleDescription.Text = "Find and arrest the criminal!"
		elseif role == "Citizen" then
			roleFrame.BackgroundColor3 = Color3.fromRGB(50, 200, 50)
			roleDescription.Text = "Help the police or stay safe!"
		else
			roleFrame.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
			roleDescription.Text = "Wait for the next round to start"
		end
	end
end

-- Update timer display
local function updateTimerDisplay(timeRemaining, gameState)
	if not mainGui then return end

	local timerText = mainGui:FindFirstChild("TimerFrame"):FindFirstChild("TimerText")

	if timerText then
		if gameState == "Waiting" then
			timerText.Text = "Waiting for players..."
		elseif gameState == "Starting" then
			timerText.Text = string.format("Round starting in %d", math.ceil(timeRemaining))
		elseif gameState == "Playing" then
			local minutes = math.floor(timeRemaining / 60)
			local seconds = math.floor(timeRemaining % 60)
			timerText.Text = string.format("Time: %02d:%02d", minutes, seconds)
		elseif gameState == "Ending" then
			timerText.Text = "Round ended!"
		end
	end
end

-- Toggle menu
function toggleMenu()
	-- This would open/close the main menu with team selection, car shop, etc.
	print("MainClient: Menu toggled")
end

-- Toggle sound
function toggleSound(enabled)
	soundToggle:InvokeServer("Music", enabled)

	-- Update button visual feedback
	if mainGui then
		local soundFrame = mainGui:FindFirstChild("SoundButtons")
		if soundFrame then
			local soundOn = soundFrame:FindFirstChild("SoundOn")
			local soundOff = soundFrame:FindFirstChild("SoundOff")

			if enabled then
				soundOn.BackgroundColor3 = Color3.fromRGB(0, 255, 0)
				soundOff.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
			else
				soundOn.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
				soundOff.BackgroundColor3 = Color3.fromRGB(255, 0, 0)
			end
		end
	end

	print("MainClient: Sound toggled -", enabled)
end

-- Handle arrest input
local function handleArrestInput()
	if currentRole ~= "Police" then return end

	-- Find nearest criminal player
	local nearestCriminal = nil
	local nearestDistance = math.huge

	for _, otherPlayer in ipairs(Players:GetPlayers()) do
		if otherPlayer ~= player and otherPlayer.Character and otherPlayer.Character.PrimaryPart then
			local distance = (player.Character.PrimaryPart.Position - 
				otherPlayer.Character.PrimaryPart.Position).Magnitude

			if distance < nearestDistance and distance <= 15 then
				nearestDistance = distance
				nearestCriminal = otherPlayer
			end
		end
	end

	if nearestCriminal then
		local success, message = arrestRequest:InvokeServer(nearestCriminal)

		-- Show arrest result
		StarterGui:SetCore("ChatMakeSystemMessage", {
			Text = message;
			Color = success and Color3.fromRGB(0, 255, 0) or Color3.fromRGB(255, 0, 0);
		})
	else
		StarterGui:SetCore("ChatMakeSystemMessage", {
			Text = "No criminals nearby to arrest!";
			Color = Color3.fromRGB(255, 255, 0);
		})
	end
end

-- Initialize client
local function initialize()
	print("MainClient: Initializing...")

	-- Create GUI elements
	createMainGui()
	createRoleGui()

	-- Connect remote events
	gameStateChanged.OnClientEvent:Connect(function(oldState, newState)
		currentGameState = newState
		print("MainClient: Game state changed to", newState)
	end)

	roleAssigned.OnClientEvent:Connect(function(role)
		updateRoleDisplay(role)
		print("MainClient: Role assigned -", role)
	end)

	roundTimer.OnClientEvent:Connect(function(timeRemaining, gameState)
		updateTimerDisplay(timeRemaining, gameState)
	end)

	playerDataUpdated.OnClientEvent:Connect(function(key, value)
		playerData[key] = value
		updatePlayerDataDisplay()
	end)

	crimeAssigned.OnClientEvent:Connect(function(crime)
		currentCrime = crime
		print("MainClient: Crime assigned -", crime.Config.Name)

		-- Show crime notification
		StarterGui:SetCore("ChatMakeSystemMessage", {
			Text = "Crime Mission: " .. crime.Config.Description;
			Color = Color3.fromRGB(255, 100, 100);
		})
	end)

	-- Connect input events
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end

		if input.KeyCode == Enum.KeyCode.E then
			handleArrestInput()
		elseif input.KeyCode == Enum.KeyCode.M then
			toggleMenu()
		end
	end)

	print("MainClient: Initialized successfully!")
end

-- Start the client
initialize()
