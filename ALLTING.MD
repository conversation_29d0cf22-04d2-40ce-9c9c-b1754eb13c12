IN ROBLOX STUDIO IN GUI ALREADY HAVE IN STARTGUI GUI CALED MainGui AND INSIDE HAVE LevelFrameAND AND Menu BUT IN LevelFrameAND HAVE BarXpLevelFRAME AND Xp_LevelText TEXTLABEL  FOR LEVEL XP . AND FOR Menu Change TeamsChange BUTTONS IMAGE FOR PLAYER TO CHOSES ATEAM BUT YOU NEED TO CREATE THE GUI OF  IT ETC  AND  in mainguii have too SoundButtons for Sound ON and OFF etc.now in workspace have a folder called npc  annd anoter thing in workspace have a folder called car for City Map Use  for Cars are parked around the map (criminals can steal them).

Police have cars at the police station for fast chases.

Citizens can drive normal cars (but slower). et cand  Gamepasses for Cars

Supercar Pass → unlocks a unique, super-fast car.

SWAT Vehicle Pass → armored truck for police.

Luxury Cars Pack → sports cars for flex & style.  and for the in startgui idont have already gui menu but create it by you  Car Shop System  car  and in Buying Cars

Players use in-game Cash to buy cars.

Once bought, the car is saved to their garage (data saving).

They can spawn their owned cars at spawn points around the map.

Car Types in Shop

Civilian Cars (cheap, slow-medium speed).

Police Cars (only for police role).

Criminal Cars (fast getaway cars, sometimes limited edition).

Special Cars (bikes, trucks, sports cars).

Upgrades & Cosmetics

Speed upgrades (engine).

Handling upgrades (tires).

Paint shop (custom colors, neon lights, police siren colors).

Skins / wraps (flames, gold, camo). etc 💸 Gamepasses for Car Shop

Supercar Pack → unlocks exclusive cars not in the normal shop.

Police SWAT Pack → armored car, faster police vehicles.

Garage Expansion → allows you to own more cars.

VIP Paints → gold, chrome, neon effects. etcand more  sooon and anoter by you idea :) thx 