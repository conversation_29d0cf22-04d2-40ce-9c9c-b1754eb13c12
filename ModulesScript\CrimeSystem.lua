-- CrimeSystem.lua - Crime mechanics and missions module
local CrimeSystem = {}

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local TweenService = game:GetService("TweenService")

-- Crime types and configurations
CrimeSystem.CrimeTypes = {
    BankRobbery = {
        Name = "Bank Robbery",
        Description = "Rob the city bank and escape with the money",
        Reward = 1000,
        XPReward = 50,
        Duration = 30, -- seconds to complete
        Difficulty = "Hard",
        Location = "Bank",
        RequiredItems = {},
        Steps = {
            "Go to the bank",
            "Hack the security system",
            "Open the vault",
            "Collect the money",
            "Escape without being caught"
        }
    },
    
    CarTheft = {
        Name = "Car Theft",
        Description = "Steal a parked car and drive it to the hideout",
        Reward = 300,
        XPReward = 20,
        Duration = 45,
        Difficulty = "Easy",
        Location = "Street",
        RequiredItems = {},
        Steps = {
            "Find a parked car",
            "Break into the car",
            "Drive to the hideout",
            "Deliver the car"
        }
    },
    
    StoreRobbery = {
        Name = "Store Robbery",
        Description = "Rob a convenience store for quick cash",
        Reward = 500,
        XPReward = 30,
        Duration = 20,
        Difficulty = "Medium",
        Location = "Store",
        RequiredItems = {},
        Steps = {
            "Enter the store",
            "Threaten the cashier",
            "Take the money",
            "Escape quickly"
        }
    },
    
    Vandalism = {
        Name = "Vandalism",
        Description = "Tag walls around the city with graffiti",
        Reward = 150,
        XPReward = 15,
        Duration = 60,
        Difficulty = "Easy",
        Location = "Various",
        RequiredItems = {"Spray Paint"},
        Steps = {
            "Find a wall to tag",
            "Spray your graffiti",
            "Avoid police detection",
            "Tag 3 different locations"
        }
    },
    
    Sabotage = {
        Name = "Sabotage",
        Description = "Sabotage police vehicles to slow down pursuit",
        Reward = 400,
        XPReward = 25,
        Duration = 25,
        Difficulty = "Medium",
        Location = "Police Station",
        RequiredItems = {"Tools"},
        Steps = {
            "Sneak into police station",
            "Find police vehicles",
            "Sabotage the engines",
            "Escape undetected"
        }
    }
}

-- Crime locations in the map
CrimeSystem.CrimeLocations = {
    Bank = {},
    Store = {},
    ParkingLot = {},
    PoliceStation = {},
    Walls = {}
}

-- Active crimes
CrimeSystem.ActiveCrimes = {}
CrimeSystem.CompletedCrimes = {}

-- Events
CrimeSystem.Events = {
    CrimeAssigned = Instance.new("BindableEvent"),
    CrimeStarted = Instance.new("BindableEvent"),
    CrimeCompleted = Instance.new("BindableEvent"),
    CrimeFailed = Instance.new("BindableEvent"),
    CrimeProgress = Instance.new("BindableEvent")
}

-- Initialize Crime System
function CrimeSystem:Initialize()
    print("CrimeSystem: Initializing...")
    
    -- Find crime locations in workspace
    self:FindCrimeLocations()
    
    print("CrimeSystem: Initialized successfully!")
end

-- Find crime locations in the workspace
function CrimeSystem:FindCrimeLocations()
    local crimeLocationsFolder = Workspace:FindFirstChild("CrimeLocations")
    if not crimeLocationsFolder then
        warn("CrimeSystem: CrimeLocations folder not found in Workspace")
        return
    end
    
    for _, location in ipairs(crimeLocationsFolder:GetChildren()) do
        if location:IsA("Part") or location:IsA("Model") then
            local locationType = location:GetAttribute("Type")
            
            if locationType and self.CrimeLocations[locationType] then
                table.insert(self.CrimeLocations[locationType], location)
            end
        end
    end
    
    print("CrimeSystem: Found crime locations -",
          "Banks:", #self.CrimeLocations.Bank,
          "Stores:", #self.CrimeLocations.Store,
          "Parking Lots:", #self.CrimeLocations.ParkingLot,
          "Police Stations:", #self.CrimeLocations.PoliceStation,
          "Walls:", #self.CrimeLocations.Walls)
end

-- Assign a random crime to a criminal
function CrimeSystem:AssignRandomCrime(player)
    local availableCrimes = {}
    
    -- Get all available crime types
    for crimeType, config in pairs(self.CrimeTypes) do
        table.insert(availableCrimes, crimeType)
    end
    
    if #availableCrimes == 0 then
        warn("CrimeSystem: No available crimes to assign")
        return nil
    end
    
    -- Select random crime
    local selectedCrime = availableCrimes[math.random(1, #availableCrimes)]
    
    return self:AssignCrime(player, selectedCrime)
end

-- Assign a specific crime to a player
function CrimeSystem:AssignCrime(player, crimeType)
    local config = self.CrimeTypes[crimeType]
    if not config then
        warn("CrimeSystem: Unknown crime type:", crimeType)
        return nil
    end
    
    -- Create crime instance
    local crime = {
        Player = player,
        Type = crimeType,
        Config = config,
        StartTime = tick(),
        CurrentStep = 1,
        IsActive = false,
        IsCompleted = false,
        Progress = {},
        Location = self:SelectCrimeLocation(config.Location)
    }
    
    -- Add to active crimes
    self.ActiveCrimes[player] = crime
    
    -- Fire event
    self.Events.CrimeAssigned:Fire(player, crime)
    
    print("CrimeSystem: Assigned", config.Name, "to", player.Name)
    return crime
end

-- Select a location for the crime
function CrimeSystem:SelectCrimeLocation(locationType)
    local locations = self.CrimeLocations[locationType]
    
    if not locations or #locations == 0 then
        warn("CrimeSystem: No locations found for type:", locationType)
        return nil
    end
    
    return locations[math.random(1, #locations)]
end

-- Start a crime
function CrimeSystem:StartCrime(player)
    local crime = self.ActiveCrimes[player]
    if not crime then
        warn("CrimeSystem: No active crime for player:", player.Name)
        return false
    end
    
    if crime.IsActive then
        warn("CrimeSystem: Crime already active for player:", player.Name)
        return false
    end
    
    crime.IsActive = true
    crime.StartTime = tick()
    
    -- Fire event
    self.Events.CrimeStarted:Fire(player, crime)
    
    print("CrimeSystem:", player.Name, "started", crime.Config.Name)
    return true
end

-- Update crime progress
function CrimeSystem:UpdateCrimeProgress(player, stepCompleted)
    local crime = self.ActiveCrimes[player]
    if not crime or not crime.IsActive then
        return false
    end
    
    -- Mark step as completed
    crime.Progress[stepCompleted] = true
    
    -- Check if all steps are completed
    local allStepsCompleted = true
    for i = 1, #crime.Config.Steps do
        if not crime.Progress[i] then
            allStepsCompleted = false
            break
        end
    end
    
    -- Fire progress event
    self.Events.CrimeProgress:Fire(player, crime, stepCompleted)
    
    if allStepsCompleted then
        self:CompleteCrime(player)
    end
    
    return true
end

-- Complete a crime
function CrimeSystem:CompleteCrime(player)
    local crime = self.ActiveCrimes[player]
    if not crime then
        return false
    end
    
    crime.IsCompleted = true
    crime.IsActive = false
    crime.CompletionTime = tick()
    
    -- Calculate rewards
    local cashReward = crime.Config.Reward
    local xpReward = crime.Config.XPReward
    
    -- Apply VIP bonus if player has VIP pass
    -- This would integrate with PlayerData module
    
    -- Move to completed crimes
    table.insert(self.CompletedCrimes, crime)
    self.ActiveCrimes[player] = nil
    
    -- Fire event
    self.Events.CrimeCompleted:Fire(player, crime, cashReward, xpReward)
    
    print("CrimeSystem:", player.Name, "completed", crime.Config.Name, 
          "- Reward:", cashReward, "cash,", xpReward, "XP")
    
    return true, cashReward, xpReward
end

-- Fail a crime (caught by police or time ran out)
function CrimeSystem:FailCrime(player, reason)
    local crime = self.ActiveCrimes[player]
    if not crime then
        return false
    end
    
    crime.IsActive = false
    crime.FailureReason = reason
    crime.FailureTime = tick()
    
    -- Remove from active crimes
    self.ActiveCrimes[player] = nil
    
    -- Fire event
    self.Events.CrimeFailed:Fire(player, crime, reason)
    
    print("CrimeSystem:", player.Name, "failed", crime.Config.Name, "- Reason:", reason)
    return true
end

-- Check if crime has timed out
function CrimeSystem:CheckCrimeTimeouts()
    for player, crime in pairs(self.ActiveCrimes) do
        if crime.IsActive then
            local timeElapsed = tick() - crime.StartTime
            
            if timeElapsed > crime.Config.Duration then
                self:FailCrime(player, "Time ran out")
            end
        end
    end
end

-- Get player's current crime
function CrimeSystem:GetPlayerCrime(player)
    return self.ActiveCrimes[player]
end

-- Get crime progress for player
function CrimeSystem:GetCrimeProgress(player)
    local crime = self.ActiveCrimes[player]
    if not crime then
        return nil
    end
    
    local completedSteps = 0
    for i = 1, #crime.Config.Steps do
        if crime.Progress[i] then
            completedSteps = completedSteps + 1
        end
    end
    
    return {
        Crime = crime,
        CompletedSteps = completedSteps,
        TotalSteps = #crime.Config.Steps,
        TimeRemaining = math.max(0, crime.Config.Duration - (tick() - crime.StartTime)),
        CurrentStep = crime.Config.Steps[completedSteps + 1]
    }
end

-- Check if player is near crime location
function CrimeSystem:IsPlayerNearLocation(player, location, distance)
    if not location or not player.Character or not player.Character.PrimaryPart then
        return false
    end
    
    local playerPosition = player.Character.PrimaryPart.Position
    local locationPosition
    
    if location:IsA("Part") then
        locationPosition = location.Position
    elseif location:IsA("Model") and location.PrimaryPart then
        locationPosition = location.PrimaryPart.Position
    else
        return false
    end
    
    return (playerPosition - locationPosition).Magnitude <= (distance or 10)
end

-- Arrest a criminal (called by police)
function CrimeSystem:ArrestCriminal(criminal, police)
    local crime = self.ActiveCrimes[criminal]
    
    if crime and crime.IsActive then
        self:FailCrime(criminal, "Arrested by " .. police.Name)
        
        -- Reward police officer
        -- This would integrate with PlayerData module
        
        print("CrimeSystem:", criminal.Name, "was arrested by", police.Name)
        return true
    end
    
    return false
end

-- Get crime statistics
function CrimeSystem:GetCrimeStats()
    local stats = {
        ActiveCrimes = 0,
        CompletedCrimes = #self.CompletedCrimes,
        CrimeTypes = {}
    }
    
    for player, crime in pairs(self.ActiveCrimes) do
        stats.ActiveCrimes = stats.ActiveCrimes + 1
    end
    
    for _, crime in ipairs(self.CompletedCrimes) do
        local crimeType = crime.Type
        stats.CrimeTypes[crimeType] = (stats.CrimeTypes[crimeType] or 0) + 1
    end
    
    return stats
end

-- Clean up old completed crimes
function CrimeSystem:CleanupOldCrimes()
    local currentTime = tick()
    
    for i = #self.CompletedCrimes, 1, -1 do
        local crime = self.CompletedCrimes[i]
        
        -- Remove crimes older than 1 hour
        if crime.CompletionTime and (currentTime - crime.CompletionTime) > 3600 then
            table.remove(self.CompletedCrimes, i)
        end
    end
end

return CrimeSystem
